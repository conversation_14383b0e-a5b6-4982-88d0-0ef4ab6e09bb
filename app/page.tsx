"use client";

import { Foot<PERSON> } from "@/components/footer";
import { AIGenerationCTA } from "@/components/home/<USER>";
import { CTA } from "@/components/home/<USER>";
import { FAQ } from "@/components/home/<USER>";
import { Features } from "@/components/home/<USER>";
import { Header } from "@/components/home/<USER>";
import { Hero } from "@/components/home/<USER>";
import { HowItWorks } from "@/components/home/<USER>";
import { Roadmap } from "@/components/home/<USER>";
import { Testimonials } from "@/components/home/<USER>";
import { ThemePresetSelector } from "@/components/home/<USER>";
import { useEffect, useState } from "react";

export default function Home() {
  const [isScrolled, setIsScrolled] = useState(false);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 10) {
        setIsScrolled(true);
      } else {
        setIsScrolled(false);
      }
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  return (
    <div className="bg-background text-foreground flex min-h-[100dvh] flex-col items-center justify-items-center">
      <Header
        isScrolled={isScrolled}
        mobileMenuOpen={mobileMenuOpen}
        setMobileMenuOpen={setMobileMenuOpen}
      />
      <main className="w-full flex-1">
        <Hero />
        <ThemePresetSelector />
        <Testimonials />
        <Features />
        <AIGenerationCTA />
        <HowItWorks />
        <Roadmap />
        <FAQ />
        <CTA />
      </main>
      <Footer />
    </div>
  );
}
