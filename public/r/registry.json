{"$schema": "https://ui.shadcn.com/schema/registry.json", "name": "tweakcn-theme-registry", "homepage": "http://tweakcn.com", "items": [{"name": "modern-minimal", "type": "registry:style", "title": "Modern Minimal", "description": "A theme based on the Modern Minimal color palette.", "css": {"@layer base": {"body": {"letter-spacing": "var(--tracking-normal)"}}}, "cssVars": {"theme": {"font-sans": "Inter, sans-serif", "font-mono": "JetBrains Mono, monospace", "font-serif": "Source Serif 4, serif", "radius": "0.375rem", "tracking-tighter": "calc(var(--tracking-normal) - 0.05em)", "tracking-tight": "calc(var(--tracking-normal) - 0.025em)", "tracking-wide": "calc(var(--tracking-normal) + 0.025em)", "tracking-wider": "calc(var(--tracking-normal) + 0.05em)", "tracking-widest": "calc(var(--tracking-normal) + 0.1em)"}, "light": {"background": "oklch(1.00 0 0)", "foreground": "oklch(0.32 0 0)", "card": "oklch(1.00 0 0)", "card-foreground": "oklch(0.32 0 0)", "popover": "oklch(1.00 0 0)", "popover-foreground": "oklch(0.32 0 0)", "primary": "oklch(0.62 0.19 259.81)", "primary-foreground": "oklch(1.00 0 0)", "secondary": "oklch(0.97 0.00 264.54)", "secondary-foreground": "oklch(0.45 0.03 256.80)", "muted": "oklch(0.98 0.00 247.84)", "muted-foreground": "oklch(0.55 0.02 264.36)", "accent": "oklch(0.95 0.03 236.82)", "accent-foreground": "oklch(0.38 0.14 265.52)", "destructive": "oklch(0.64 0.21 25.33)", "destructive-foreground": "oklch(1.00 0 0)", "border": "oklch(0.93 0.01 264.53)", "input": "oklch(0.93 0.01 264.53)", "ring": "oklch(0.62 0.19 259.81)", "chart-1": "oklch(0.62 0.19 259.81)", "chart-2": "oklch(0.55 0.22 262.88)", "chart-3": "oklch(0.49 0.22 264.38)", "chart-4": "oklch(0.42 0.18 265.64)", "chart-5": "oklch(0.38 0.14 265.52)", "radius": "0.375rem", "sidebar": "oklch(0.98 0.00 247.84)", "sidebar-foreground": "oklch(0.32 0 0)", "sidebar-primary": "oklch(0.62 0.19 259.81)", "sidebar-primary-foreground": "oklch(1.00 0 0)", "sidebar-accent": "oklch(0.95 0.03 236.82)", "sidebar-accent-foreground": "oklch(0.38 0.14 265.52)", "sidebar-border": "oklch(0.93 0.01 264.53)", "sidebar-ring": "oklch(0.62 0.19 259.81)", "font-sans": "Inter, sans-serif", "font-serif": "Source Serif 4, serif", "font-mono": "JetBrains Mono, monospace", "shadow-color": "hsl(0 0% 0%)", "shadow-opacity": "0.1", "shadow-blur": "3px", "shadow-spread": "0px", "shadow-offset-x": "0", "shadow-offset-y": "1px", "letter-spacing": "0em", "spacing": "0.25rem", "shadow-2xs": "0 1px 3px 0px hsl(0 0% 0% / 0.05)", "shadow-xs": "0 1px 3px 0px hsl(0 0% 0% / 0.05)", "shadow-sm": "0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 1px 2px -1px hsl(0 0% 0% / 0.10)", "shadow": "0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 1px 2px -1px hsl(0 0% 0% / 0.10)", "shadow-md": "0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 2px 4px -1px hsl(0 0% 0% / 0.10)", "shadow-lg": "0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 4px 6px -1px hsl(0 0% 0% / 0.10)", "shadow-xl": "0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 8px 10px -1px hsl(0 0% 0% / 0.10)", "shadow-2xl": "0 1px 3px 0px hsl(0 0% 0% / 0.25)", "tracking-normal": "0em"}, "dark": {"background": "oklch(0.20 0 0)", "foreground": "oklch(0.92 0 0)", "card": "oklch(0.27 0 0)", "card-foreground": "oklch(0.92 0 0)", "popover": "oklch(0.27 0 0)", "popover-foreground": "oklch(0.92 0 0)", "primary": "oklch(0.62 0.19 259.81)", "primary-foreground": "oklch(1.00 0 0)", "secondary": "oklch(0.27 0 0)", "secondary-foreground": "oklch(0.92 0 0)", "muted": "oklch(0.27 0 0)", "muted-foreground": "oklch(0.72 0 0)", "accent": "oklch(0.38 0.14 265.52)", "accent-foreground": "oklch(0.88 0.06 254.13)", "destructive": "oklch(0.64 0.21 25.33)", "destructive-foreground": "oklch(1.00 0 0)", "border": "oklch(0.37 0 0)", "input": "oklch(0.37 0 0)", "ring": "oklch(0.62 0.19 259.81)", "chart-1": "oklch(0.71 0.14 254.62)", "chart-2": "oklch(0.62 0.19 259.81)", "chart-3": "oklch(0.55 0.22 262.88)", "chart-4": "oklch(0.49 0.22 264.38)", "chart-5": "oklch(0.42 0.18 265.64)", "radius": "0.375rem", "sidebar": "oklch(0.20 0 0)", "sidebar-foreground": "oklch(0.92 0 0)", "sidebar-primary": "oklch(0.62 0.19 259.81)", "sidebar-primary-foreground": "oklch(1.00 0 0)", "sidebar-accent": "oklch(0.38 0.14 265.52)", "sidebar-accent-foreground": "oklch(0.88 0.06 254.13)", "sidebar-border": "oklch(0.37 0 0)", "sidebar-ring": "oklch(0.62 0.19 259.81)", "font-sans": "Inter, sans-serif", "font-serif": "Source Serif 4, serif", "font-mono": "JetBrains Mono, monospace", "shadow-color": "hsl(0 0% 0%)", "shadow-opacity": "0.1", "shadow-blur": "3px", "shadow-spread": "0px", "shadow-offset-x": "0", "shadow-offset-y": "1px", "letter-spacing": "0em", "spacing": "0.25rem", "shadow-2xs": "0 1px 3px 0px hsl(0 0% 0% / 0.05)", "shadow-xs": "0 1px 3px 0px hsl(0 0% 0% / 0.05)", "shadow-sm": "0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 1px 2px -1px hsl(0 0% 0% / 0.10)", "shadow": "0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 1px 2px -1px hsl(0 0% 0% / 0.10)", "shadow-md": "0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 2px 4px -1px hsl(0 0% 0% / 0.10)", "shadow-lg": "0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 4px 6px -1px hsl(0 0% 0% / 0.10)", "shadow-xl": "0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 8px 10px -1px hsl(0 0% 0% / 0.10)", "shadow-2xl": "0 1px 3px 0px hsl(0 0% 0% / 0.25)"}}}, {"name": "t3-chat", "type": "registry:style", "title": "T3 Chat", "description": "A theme based on the T3 Chat color palette.", "css": {"@layer base": {"body": {"letter-spacing": "var(--tracking-normal)"}}}, "cssVars": {"theme": {"font-sans": "ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', <PERSON>l, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji'", "font-mono": "ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, \"Liberation Mono\", \"Courier New\", monospace", "font-serif": "ui-serif, Georgia, Cambria, \"Times New Roman\", Times, serif", "radius": "0.5rem", "tracking-tighter": "calc(var(--tracking-normal) - 0.05em)", "tracking-tight": "calc(var(--tracking-normal) - 0.025em)", "tracking-wide": "calc(var(--tracking-normal) + 0.025em)", "tracking-wider": "calc(var(--tracking-normal) + 0.05em)", "tracking-widest": "calc(var(--tracking-normal) + 0.1em)"}, "light": {"background": "oklch(0.98 0.01 325.64)", "foreground": "oklch(0.33 0.12 325.04)", "card": "oklch(0.98 0.01 325.64)", "card-foreground": "oklch(0.33 0.12 325.04)", "popover": "oklch(1.00 0 0)", "popover-foreground": "oklch(0.33 0.12 325.04)", "primary": "oklch(0.53 0.14 355.20)", "primary-foreground": "oklch(1.00 0 0)", "secondary": "oklch(0.87 0.07 334.90)", "secondary-foreground": "oklch(0.44 0.13 324.80)", "muted": "oklch(0.94 0.03 331.55)", "muted-foreground": "oklch(0.49 0.12 324.45)", "accent": "oklch(0.87 0.07 334.90)", "accent-foreground": "oklch(0.44 0.13 324.80)", "destructive": "oklch(0.52 0.14 20.83)", "destructive-foreground": "oklch(1.00 0 0)", "border": "oklch(0.86 0.08 328.91)", "input": "oklch(0.85 0.06 336.60)", "ring": "oklch(0.59 0.22 0.58)", "chart-1": "oklch(0.60 0.24 344.47)", "chart-2": "oklch(0.44 0.23 300.62)", "chart-3": "oklch(0.38 0.04 226.15)", "chart-4": "oklch(0.83 0.12 88.35)", "chart-5": "oklch(0.78 0.13 59.00)", "radius": "0.5rem", "sidebar": "oklch(0.94 0.03 320.58)", "sidebar-foreground": "oklch(0.49 0.19 354.54)", "sidebar-primary": "oklch(0.40 0.03 285.20)", "sidebar-primary-foreground": "oklch(0.97 0.01 337.52)", "sidebar-accent": "oklch(0.98 0.00 106.42)", "sidebar-accent-foreground": "oklch(0.40 0.03 285.20)", "sidebar-border": "oklch(0.94 0.00 48.72)", "sidebar-ring": "oklch(0.59 0.22 0.58)", "font-sans": "ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', <PERSON>l, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji'", "font-serif": "ui-serif, Georgia, Cambria, \"Times New Roman\", Times, serif", "font-mono": "ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, \"Liberation Mono\", \"Courier New\", monospace", "shadow-color": "hsl(0 0% 0%)", "shadow-opacity": "0.1", "shadow-blur": "3px", "shadow-spread": "0px", "shadow-offset-x": "0", "shadow-offset-y": "1px", "letter-spacing": "0em", "spacing": "0.25rem", "shadow-2xs": "0 1px 3px 0px hsl(0 0% 0% / 0.05)", "shadow-xs": "0 1px 3px 0px hsl(0 0% 0% / 0.05)", "shadow-sm": "0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 1px 2px -1px hsl(0 0% 0% / 0.10)", "shadow": "0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 1px 2px -1px hsl(0 0% 0% / 0.10)", "shadow-md": "0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 2px 4px -1px hsl(0 0% 0% / 0.10)", "shadow-lg": "0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 4px 6px -1px hsl(0 0% 0% / 0.10)", "shadow-xl": "0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 8px 10px -1px hsl(0 0% 0% / 0.10)", "shadow-2xl": "0 1px 3px 0px hsl(0 0% 0% / 0.25)", "tracking-normal": "0em"}, "dark": {"background": "oklch(0.24 0.02 307.53)", "foreground": "oklch(0.84 0.04 309.54)", "card": "oklch(0.28 0.02 307.54)", "card-foreground": "oklch(0.85 0.03 341.46)", "popover": "oklch(0.15 0.01 338.90)", "popover-foreground": "oklch(0.96 0.01 341.80)", "primary": "oklch(0.46 0.19 4.10)", "primary-foreground": "oklch(0.86 0.06 346.37)", "secondary": "oklch(0.31 0.03 310.06)", "secondary-foreground": "oklch(0.85 0.04 307.96)", "muted": "oklch(0.26 0.02 309.47)", "muted-foreground": "oklch(0.79 0.04 307.10)", "accent": "oklch(0.36 0.05 308.49)", "accent-foreground": "oklch(0.96 0.01 341.80)", "destructive": "oklch(0.23 0.05 12.61)", "destructive-foreground": "oklch(1.00 0 0)", "border": "oklch(0.33 0.02 343.45)", "input": "oklch(0.34 0.02 332.83)", "ring": "oklch(0.59 0.22 0.58)", "chart-1": "oklch(0.53 0.14 355.20)", "chart-2": "oklch(0.56 0.19 306.86)", "chart-3": "oklch(0.72 0.15 60.58)", "chart-4": "oklch(0.62 0.20 312.74)", "chart-5": "oklch(0.61 0.21 6.14)", "radius": "0.5rem", "sidebar": "oklch(0.19 0.02 331.05)", "sidebar-foreground": "oklch(0.86 0.03 343.66)", "sidebar-primary": "oklch(0.49 0.22 264.38)", "sidebar-primary-foreground": "oklch(1.00 0 0)", "sidebar-accent": "oklch(0.23 0.03 338.20)", "sidebar-accent-foreground": "oklch(0.97 0.00 286.38)", "sidebar-border": "oklch(0 0 0)", "sidebar-ring": "oklch(0.59 0.22 0.58)", "font-sans": "ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', <PERSON>l, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji'", "font-serif": "ui-serif, Georgia, Cambria, \"Times New Roman\", Times, serif", "font-mono": "ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, \"Liberation Mono\", \"Courier New\", monospace", "shadow-color": "hsl(0 0% 0%)", "shadow-opacity": "0.1", "shadow-blur": "3px", "shadow-spread": "0px", "shadow-offset-x": "0", "shadow-offset-y": "1px", "letter-spacing": "0em", "spacing": "0.25rem", "shadow-2xs": "0 1px 3px 0px hsl(0 0% 0% / 0.05)", "shadow-xs": "0 1px 3px 0px hsl(0 0% 0% / 0.05)", "shadow-sm": "0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 1px 2px -1px hsl(0 0% 0% / 0.10)", "shadow": "0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 1px 2px -1px hsl(0 0% 0% / 0.10)", "shadow-md": "0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 2px 4px -1px hsl(0 0% 0% / 0.10)", "shadow-lg": "0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 4px 6px -1px hsl(0 0% 0% / 0.10)", "shadow-xl": "0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 8px 10px -1px hsl(0 0% 0% / 0.10)", "shadow-2xl": "0 1px 3px 0px hsl(0 0% 0% / 0.25)"}}}, {"name": "twitter", "type": "registry:style", "title": "Twitter", "description": "A theme based on the Twitter color palette.", "css": {"@layer base": {"body": {"letter-spacing": "var(--tracking-normal)"}}}, "cssVars": {"theme": {"font-sans": "Open Sans, sans-serif", "font-mono": "Menlo, monospace", "font-serif": "Georgia, serif", "radius": "1.3rem", "tracking-tighter": "calc(var(--tracking-normal) - 0.05em)", "tracking-tight": "calc(var(--tracking-normal) - 0.025em)", "tracking-wide": "calc(var(--tracking-normal) + 0.025em)", "tracking-wider": "calc(var(--tracking-normal) + 0.05em)", "tracking-widest": "calc(var(--tracking-normal) + 0.1em)"}, "light": {"background": "oklch(1.00 0 0)", "foreground": "oklch(0.19 0.01 248.51)", "card": "oklch(0.98 0.00 197.14)", "card-foreground": "oklch(0.19 0.01 248.51)", "popover": "oklch(1.00 0 0)", "popover-foreground": "oklch(0.19 0.01 248.51)", "primary": "oklch(0.67 0.16 245.00)", "primary-foreground": "oklch(1.00 0 0)", "secondary": "oklch(0.19 0.01 248.51)", "secondary-foreground": "oklch(1.00 0 0)", "muted": "oklch(0.92 0.00 286.37)", "muted-foreground": "oklch(0.19 0.01 248.51)", "accent": "oklch(0.94 0.02 250.85)", "accent-foreground": "oklch(0.67 0.16 245.00)", "destructive": "oklch(0.62 0.24 25.77)", "destructive-foreground": "oklch(1.00 0 0)", "border": "oklch(0.93 0.01 231.66)", "input": "oklch(0.98 0.00 228.78)", "ring": "oklch(0.68 0.16 243.35)", "chart-1": "oklch(0.67 0.16 245.00)", "chart-2": "oklch(0.69 0.16 160.35)", "chart-3": "oklch(0.82 0.16 82.53)", "chart-4": "oklch(0.71 0.18 151.71)", "chart-5": "oklch(0.59 0.22 10.58)", "radius": "1.3rem", "sidebar": "oklch(0.98 0.00 197.14)", "sidebar-foreground": "oklch(0.19 0.01 248.51)", "sidebar-primary": "oklch(0.67 0.16 245.00)", "sidebar-primary-foreground": "oklch(1.00 0 0)", "sidebar-accent": "oklch(0.94 0.02 250.85)", "sidebar-accent-foreground": "oklch(0.67 0.16 245.00)", "sidebar-border": "oklch(0.93 0.01 238.52)", "sidebar-ring": "oklch(0.68 0.16 243.35)", "font-sans": "Open Sans, sans-serif", "font-serif": "Georgia, serif", "font-mono": "Menlo, monospace", "shadow-color": "rgba(29,161,242,0.15)", "shadow-opacity": "0", "shadow-blur": "0px", "shadow-spread": "0px", "shadow-offset-x": "0px", "shadow-offset-y": "2px", "letter-spacing": "0em", "spacing": "0.25rem", "shadow-2xs": "0px 2px 0px 0px hsl(202.82 89.12% 53.14% / 0.00)", "shadow-xs": "0px 2px 0px 0px hsl(202.82 89.12% 53.14% / 0.00)", "shadow-sm": "0px 2px 0px 0px hsl(202.82 89.12% 53.14% / 0.00), 0px 1px 2px -1px hsl(202.82 89.12% 53.14% / 0.00)", "shadow": "0px 2px 0px 0px hsl(202.82 89.12% 53.14% / 0.00), 0px 1px 2px -1px hsl(202.82 89.12% 53.14% / 0.00)", "shadow-md": "0px 2px 0px 0px hsl(202.82 89.12% 53.14% / 0.00), 0px 2px 4px -1px hsl(202.82 89.12% 53.14% / 0.00)", "shadow-lg": "0px 2px 0px 0px hsl(202.82 89.12% 53.14% / 0.00), 0px 4px 6px -1px hsl(202.82 89.12% 53.14% / 0.00)", "shadow-xl": "0px 2px 0px 0px hsl(202.82 89.12% 53.14% / 0.00), 0px 8px 10px -1px hsl(202.82 89.12% 53.14% / 0.00)", "shadow-2xl": "0px 2px 0px 0px hsl(202.82 89.12% 53.14% / 0.00)", "tracking-normal": "0em"}, "dark": {"background": "oklch(0 0 0)", "foreground": "oklch(0.93 0.00 228.79)", "card": "oklch(0.21 0.01 274.53)", "card-foreground": "oklch(0.89 0 0)", "popover": "oklch(0 0 0)", "popover-foreground": "oklch(0.93 0.00 228.79)", "primary": "oklch(0.67 0.16 245.01)", "primary-foreground": "oklch(1.00 0 0)", "secondary": "oklch(0.96 0.00 219.53)", "secondary-foreground": "oklch(0.19 0.01 248.51)", "muted": "oklch(0.21 0 0)", "muted-foreground": "oklch(0.56 0.01 247.97)", "accent": "oklch(0.19 0.03 242.55)", "accent-foreground": "oklch(0.67 0.16 245.01)", "destructive": "oklch(0.62 0.24 25.77)", "destructive-foreground": "oklch(1.00 0 0)", "border": "oklch(0.27 0.00 248.00)", "input": "oklch(0.30 0.03 244.82)", "ring": "oklch(0.68 0.16 243.35)", "chart-1": "oklch(0.67 0.16 245.00)", "chart-2": "oklch(0.69 0.16 160.35)", "chart-3": "oklch(0.82 0.16 82.53)", "chart-4": "oklch(0.71 0.18 151.71)", "chart-5": "oklch(0.59 0.22 10.58)", "radius": "1.3rem", "sidebar": "oklch(0.21 0.01 274.53)", "sidebar-foreground": "oklch(0.89 0 0)", "sidebar-primary": "oklch(0.68 0.16 243.35)", "sidebar-primary-foreground": "oklch(1.00 0 0)", "sidebar-accent": "oklch(0.19 0.03 242.55)", "sidebar-accent-foreground": "oklch(0.67 0.16 245.01)", "sidebar-border": "oklch(0.38 0.02 240.59)", "sidebar-ring": "oklch(0.68 0.16 243.35)", "font-sans": "Open Sans, sans-serif", "font-serif": "Georgia, serif", "font-mono": "Menlo, monospace", "shadow-color": "rgba(29,161,242,0.25)", "shadow-opacity": "0", "shadow-blur": "0px", "shadow-spread": "0px", "shadow-offset-x": "0px", "shadow-offset-y": "2px", "letter-spacing": "0em", "spacing": "0.25rem", "shadow-2xs": "0px 2px 0px 0px hsl(202.82 89.12% 53.14% / 0.00)", "shadow-xs": "0px 2px 0px 0px hsl(202.82 89.12% 53.14% / 0.00)", "shadow-sm": "0px 2px 0px 0px hsl(202.82 89.12% 53.14% / 0.00), 0px 1px 2px -1px hsl(202.82 89.12% 53.14% / 0.00)", "shadow": "0px 2px 0px 0px hsl(202.82 89.12% 53.14% / 0.00), 0px 1px 2px -1px hsl(202.82 89.12% 53.14% / 0.00)", "shadow-md": "0px 2px 0px 0px hsl(202.82 89.12% 53.14% / 0.00), 0px 2px 4px -1px hsl(202.82 89.12% 53.14% / 0.00)", "shadow-lg": "0px 2px 0px 0px hsl(202.82 89.12% 53.14% / 0.00), 0px 4px 6px -1px hsl(202.82 89.12% 53.14% / 0.00)", "shadow-xl": "0px 2px 0px 0px hsl(202.82 89.12% 53.14% / 0.00), 0px 8px 10px -1px hsl(202.82 89.12% 53.14% / 0.00)", "shadow-2xl": "0px 2px 0px 0px hsl(202.82 89.12% 53.14% / 0.00)"}}}, {"name": "mocha-mousse", "type": "registry:style", "title": "<PERSON><PERSON>", "description": "A theme based on the <PERSON>cha <PERSON> color palette.", "css": {"@layer base": {"body": {"letter-spacing": "var(--tracking-normal)"}}}, "cssVars": {"theme": {"font-sans": "DM Sans, sans-serif", "font-mono": "Menlo, monospace", "font-serif": "Georgia, serif", "radius": "0.5rem", "tracking-tighter": "calc(var(--tracking-normal) - 0.05em)", "tracking-tight": "calc(var(--tracking-normal) - 0.025em)", "tracking-wide": "calc(var(--tracking-normal) + 0.025em)", "tracking-wider": "calc(var(--tracking-normal) + 0.05em)", "tracking-widest": "calc(var(--tracking-normal) + 0.1em)"}, "light": {"background": "oklch(0.95 0.01 102.46)", "foreground": "oklch(0.41 0.03 40.36)", "card": "oklch(0.95 0.01 102.46)", "card-foreground": "oklch(0.41 0.03 40.36)", "popover": "oklch(1.00 0 0)", "popover-foreground": "oklch(0.41 0.03 40.36)", "primary": "oklch(0.61 0.06 44.36)", "primary-foreground": "oklch(1.00 0 0)", "secondary": "oklch(0.75 0.04 80.55)", "secondary-foreground": "oklch(1.00 0 0)", "muted": "oklch(0.85 0.04 49.09)", "muted-foreground": "oklch(0.54 0.05 37.21)", "accent": "oklch(0.85 0.04 49.09)", "accent-foreground": "oklch(0.41 0.03 40.36)", "destructive": "oklch(0.22 0.01 52.96)", "destructive-foreground": "oklch(1.00 0 0)", "border": "oklch(0.75 0.04 80.55)", "input": "oklch(0.75 0.04 80.55)", "ring": "oklch(0.61 0.06 44.36)", "chart-1": "oklch(0.61 0.06 44.36)", "chart-2": "oklch(0.54 0.05 37.21)", "chart-3": "oklch(0.73 0.05 52.33)", "chart-4": "oklch(0.75 0.04 80.55)", "chart-5": "oklch(0.64 0.04 52.39)", "radius": "0.5rem", "sidebar": "oklch(0.89 0.03 49.57)", "sidebar-foreground": "oklch(0.41 0.03 40.36)", "sidebar-primary": "oklch(0.61 0.06 44.36)", "sidebar-primary-foreground": "oklch(1.00 0 0)", "sidebar-accent": "oklch(0.73 0.05 52.33)", "sidebar-accent-foreground": "oklch(1.00 0 0)", "sidebar-border": "oklch(0.64 0.04 52.39)", "sidebar-ring": "oklch(0.61 0.06 44.36)", "font-sans": "DM Sans, sans-serif", "font-serif": "Georgia, serif", "font-mono": "Menlo, monospace", "shadow-color": "hsl(20 18% 51% / 0.4)", "shadow-opacity": "0.11", "shadow-blur": "0px", "shadow-spread": "0px", "shadow-offset-x": "2px", "shadow-offset-y": "2px", "letter-spacing": "0em", "spacing": "0.25rem", "shadow-2xs": "2px 2px 0px 0px hsl(20 18% 51% / 0.06)", "shadow-xs": "2px 2px 0px 0px hsl(20 18% 51% / 0.06)", "shadow-sm": "2px 2px 0px 0px hsl(20 18% 51% / 0.11), 2px 1px 2px -1px hsl(20 18% 51% / 0.11)", "shadow": "2px 2px 0px 0px hsl(20 18% 51% / 0.11), 2px 1px 2px -1px hsl(20 18% 51% / 0.11)", "shadow-md": "2px 2px 0px 0px hsl(20 18% 51% / 0.11), 2px 2px 4px -1px hsl(20 18% 51% / 0.11)", "shadow-lg": "2px 2px 0px 0px hsl(20 18% 51% / 0.11), 2px 4px 6px -1px hsl(20 18% 51% / 0.11)", "shadow-xl": "2px 2px 0px 0px hsl(20 18% 51% / 0.11), 2px 8px 10px -1px hsl(20 18% 51% / 0.11)", "shadow-2xl": "2px 2px 0px 0px hsl(20 18% 51% / 0.28)", "tracking-normal": "0em"}, "dark": {"background": "oklch(0.27 0.01 48.18)", "foreground": "oklch(0.95 0.01 102.46)", "card": "oklch(0.33 0.02 50.89)", "card-foreground": "oklch(0.95 0.01 102.46)", "popover": "oklch(0.33 0.02 50.89)", "popover-foreground": "oklch(0.95 0.01 102.46)", "primary": "oklch(0.73 0.05 52.33)", "primary-foreground": "oklch(0.27 0.01 48.18)", "secondary": "oklch(0.54 0.05 37.21)", "secondary-foreground": "oklch(0.95 0.01 102.46)", "muted": "oklch(0.41 0.03 40.36)", "muted-foreground": "oklch(0.76 0.04 50.86)", "accent": "oklch(0.75 0.04 80.55)", "accent-foreground": "oklch(0.27 0.01 48.18)", "destructive": "oklch(0.69 0.14 21.46)", "destructive-foreground": "oklch(0.27 0.01 48.18)", "border": "oklch(0.41 0.03 40.36)", "input": "oklch(0.41 0.03 40.36)", "ring": "oklch(0.73 0.05 52.33)", "chart-1": "oklch(0.73 0.05 52.33)", "chart-2": "oklch(0.75 0.04 80.55)", "chart-3": "oklch(0.61 0.06 44.36)", "chart-4": "oklch(0.54 0.05 37.21)", "chart-5": "oklch(0.64 0.04 52.39)", "radius": "0.5rem", "sidebar": "oklch(0.22 0.01 52.96)", "sidebar-foreground": "oklch(0.95 0.01 102.46)", "sidebar-primary": "oklch(0.73 0.05 52.33)", "sidebar-primary-foreground": "oklch(0.22 0.01 52.96)", "sidebar-accent": "oklch(0.75 0.04 80.55)", "sidebar-accent-foreground": "oklch(0.22 0.01 52.96)", "sidebar-border": "oklch(0.41 0.03 40.36)", "sidebar-ring": "oklch(0.73 0.05 52.33)", "font-sans": "DM Sans, sans-serif", "font-serif": "Georgia, serif", "font-mono": "Menlo, monospace", "shadow-color": "hsl(20 18% 30% / 0.5)", "shadow-opacity": "0.11", "shadow-blur": "0px", "shadow-spread": "0px", "shadow-offset-x": "2px", "shadow-offset-y": "2px", "letter-spacing": "0em", "spacing": "0.25rem", "shadow-2xs": "2px 2px 0px 0px hsl(20 18% 30% / 0.06)", "shadow-xs": "2px 2px 0px 0px hsl(20 18% 30% / 0.06)", "shadow-sm": "2px 2px 0px 0px hsl(20 18% 30% / 0.11), 2px 1px 2px -1px hsl(20 18% 30% / 0.11)", "shadow": "2px 2px 0px 0px hsl(20 18% 30% / 0.11), 2px 1px 2px -1px hsl(20 18% 30% / 0.11)", "shadow-md": "2px 2px 0px 0px hsl(20 18% 30% / 0.11), 2px 2px 4px -1px hsl(20 18% 30% / 0.11)", "shadow-lg": "2px 2px 0px 0px hsl(20 18% 30% / 0.11), 2px 4px 6px -1px hsl(20 18% 30% / 0.11)", "shadow-xl": "2px 2px 0px 0px hsl(20 18% 30% / 0.11), 2px 8px 10px -1px hsl(20 18% 30% / 0.11)", "shadow-2xl": "2px 2px 0px 0px hsl(20 18% 30% / 0.28)"}}}, {"name": "bubblegum", "type": "registry:style", "title": "Bubblegum", "description": "A theme based on the Bubblegum color palette.", "css": {"@layer base": {"body": {"letter-spacing": "var(--tracking-normal)"}}}, "cssVars": {"theme": {"font-sans": "<PERSON><PERSON><PERSON>, sans-serif", "font-mono": "Fira Code, monospace", "font-serif": "Lo<PERSON>, serif", "radius": "0.4rem", "tracking-tighter": "calc(var(--tracking-normal) - 0.05em)", "tracking-tight": "calc(var(--tracking-normal) - 0.025em)", "tracking-wide": "calc(var(--tracking-normal) + 0.025em)", "tracking-wider": "calc(var(--tracking-normal) + 0.05em)", "tracking-widest": "calc(var(--tracking-normal) + 0.1em)"}, "light": {"background": "oklch(0.94 0.02 345.70)", "foreground": "oklch(0.47 0 0)", "card": "oklch(0.95 0.05 86.89)", "card-foreground": "oklch(0.47 0 0)", "popover": "oklch(1.00 0 0)", "popover-foreground": "oklch(0.47 0 0)", "primary": "oklch(0.62 0.18 348.14)", "primary-foreground": "oklch(1.00 0 0)", "secondary": "oklch(0.81 0.07 198.19)", "secondary-foreground": "oklch(0.32 0 0)", "muted": "oklch(0.88 0.05 212.10)", "muted-foreground": "oklch(0.58 0 0)", "accent": "oklch(0.92 0.08 87.67)", "accent-foreground": "oklch(0.32 0 0)", "destructive": "oklch(0.71 0.17 21.96)", "destructive-foreground": "oklch(1.00 0 0)", "border": "oklch(0.62 0.18 348.14)", "input": "oklch(0.92 0 0)", "ring": "oklch(0.70 0.16 350.75)", "chart-1": "oklch(0.70 0.16 350.75)", "chart-2": "oklch(0.82 0.08 212.09)", "chart-3": "oklch(0.92 0.08 87.67)", "chart-4": "oklch(0.80 0.11 348.18)", "chart-5": "oklch(0.62 0.19 353.91)", "radius": "0.4rem", "sidebar": "oklch(0.91 0.04 343.09)", "sidebar-foreground": "oklch(0.32 0 0)", "sidebar-primary": "oklch(0.66 0.21 354.31)", "sidebar-primary-foreground": "oklch(1.00 0 0)", "sidebar-accent": "oklch(0.82 0.11 346.02)", "sidebar-accent-foreground": "oklch(0.32 0 0)", "sidebar-border": "oklch(0.95 0.03 307.17)", "sidebar-ring": "oklch(0.66 0.21 354.31)", "font-sans": "<PERSON><PERSON><PERSON>, sans-serif", "font-serif": "Lo<PERSON>, serif", "font-mono": "Fira Code, monospace", "shadow-color": "hsl(325.78 58.18% 56.86% / 0.5)", "shadow-opacity": "1.0", "shadow-blur": "0px", "shadow-spread": "0px", "shadow-offset-x": "3px", "shadow-offset-y": "3px", "letter-spacing": "0em", "spacing": "0.25rem", "shadow-2xs": "3px 3px 0px 0px hsl(325.78 58.18% 56.86% / 0.50)", "shadow-xs": "3px 3px 0px 0px hsl(325.78 58.18% 56.86% / 0.50)", "shadow-sm": "3px 3px 0px 0px hsl(325.78 58.18% 56.86% / 1.00), 3px 1px 2px -1px hsl(325.78 58.18% 56.86% / 1.00)", "shadow": "3px 3px 0px 0px hsl(325.78 58.18% 56.86% / 1.00), 3px 1px 2px -1px hsl(325.78 58.18% 56.86% / 1.00)", "shadow-md": "3px 3px 0px 0px hsl(325.78 58.18% 56.86% / 1.00), 3px 2px 4px -1px hsl(325.78 58.18% 56.86% / 1.00)", "shadow-lg": "3px 3px 0px 0px hsl(325.78 58.18% 56.86% / 1.00), 3px 4px 6px -1px hsl(325.78 58.18% 56.86% / 1.00)", "shadow-xl": "3px 3px 0px 0px hsl(325.78 58.18% 56.86% / 1.00), 3px 8px 10px -1px hsl(325.78 58.18% 56.86% / 1.00)", "shadow-2xl": "3px 3px 0px 0px hsl(325.78 58.18% 56.86% / 2.50)", "tracking-normal": "0em"}, "dark": {"background": "oklch(0.25 0.03 234.16)", "foreground": "oklch(0.93 0.02 349.08)", "card": "oklch(0.29 0.03 233.54)", "card-foreground": "oklch(0.93 0.02 349.08)", "popover": "oklch(0.29 0.03 233.54)", "popover-foreground": "oklch(0.93 0.02 349.08)", "primary": "oklch(0.92 0.08 87.67)", "primary-foreground": "oklch(0.25 0.03 234.16)", "secondary": "oklch(0.78 0.08 4.13)", "secondary-foreground": "oklch(0.25 0.03 234.16)", "muted": "oklch(0.27 0.01 255.58)", "muted-foreground": "oklch(0.78 0.08 4.13)", "accent": "oklch(0.67 0.10 356.98)", "accent-foreground": "oklch(0.93 0.02 349.08)", "destructive": "oklch(0.67 0.18 350.36)", "destructive-foreground": "oklch(0.25 0.03 234.16)", "border": "oklch(0.39 0.04 242.22)", "input": "oklch(0.31 0.03 232.00)", "ring": "oklch(0.70 0.09 201.87)", "chart-1": "oklch(0.70 0.09 201.87)", "chart-2": "oklch(0.78 0.08 4.13)", "chart-3": "oklch(0.67 0.10 356.98)", "chart-4": "oklch(0.44 0.07 217.08)", "chart-5": "oklch(0.27 0.01 255.58)", "radius": "0.4rem", "sidebar": "oklch(0.23 0.03 235.97)", "sidebar-foreground": "oklch(0.97 0.00 264.54)", "sidebar-primary": "oklch(0.66 0.21 354.31)", "sidebar-primary-foreground": "oklch(1.00 0 0)", "sidebar-accent": "oklch(0.82 0.11 346.02)", "sidebar-accent-foreground": "oklch(0.28 0.03 256.85)", "sidebar-border": "oklch(0.37 0.03 259.73)", "sidebar-ring": "oklch(0.66 0.21 354.31)", "font-sans": "<PERSON><PERSON><PERSON>, sans-serif", "font-serif": "Lo<PERSON>, serif", "font-mono": "Fira Code, monospace", "shadow-color": "#324859", "shadow-opacity": "1.0", "shadow-blur": "0px", "shadow-spread": "0px", "shadow-offset-x": "3px", "shadow-offset-y": "3px", "letter-spacing": "0em", "spacing": "0.25rem", "shadow-2xs": "3px 3px 0px 0px hsl(206.15 28.06% 27.25% / 0.50)", "shadow-xs": "3px 3px 0px 0px hsl(206.15 28.06% 27.25% / 0.50)", "shadow-sm": "3px 3px 0px 0px hsl(206.15 28.06% 27.25% / 1.00), 3px 1px 2px -1px hsl(206.15 28.06% 27.25% / 1.00)", "shadow": "3px 3px 0px 0px hsl(206.15 28.06% 27.25% / 1.00), 3px 1px 2px -1px hsl(206.15 28.06% 27.25% / 1.00)", "shadow-md": "3px 3px 0px 0px hsl(206.15 28.06% 27.25% / 1.00), 3px 2px 4px -1px hsl(206.15 28.06% 27.25% / 1.00)", "shadow-lg": "3px 3px 0px 0px hsl(206.15 28.06% 27.25% / 1.00), 3px 4px 6px -1px hsl(206.15 28.06% 27.25% / 1.00)", "shadow-xl": "3px 3px 0px 0px hsl(206.15 28.06% 27.25% / 1.00), 3px 8px 10px -1px hsl(206.15 28.06% 27.25% / 1.00)", "shadow-2xl": "3px 3px 0px 0px hsl(206.15 28.06% 27.25% / 2.50)"}}}, {"name": "doom-64", "type": "registry:style", "title": "Doom 64", "description": "A theme based on the Doom 64 color palette.", "css": {"@layer base": {"body": {"letter-spacing": "var(--tracking-normal)"}}}, "cssVars": {"theme": {"font-sans": "\"Oxanium\", sans-serif", "font-mono": "\"Source Code Pro\", monospace", "font-serif": "ui-serif, Georgia, Cambria, \"Times New Roman\", Times, serif", "radius": "0px", "tracking-tighter": "calc(var(--tracking-normal) - 0.05em)", "tracking-tight": "calc(var(--tracking-normal) - 0.025em)", "tracking-wide": "calc(var(--tracking-normal) + 0.025em)", "tracking-wider": "calc(var(--tracking-normal) + 0.05em)", "tracking-widest": "calc(var(--tracking-normal) + 0.1em)"}, "light": {"background": "oklch(0.85 0 0)", "foreground": "oklch(0.24 0 0)", "card": "oklch(0.76 0 0)", "card-foreground": "oklch(0.24 0 0)", "popover": "oklch(0.76 0 0)", "popover-foreground": "oklch(0.24 0 0)", "primary": "oklch(0.50 0.19 27.48)", "primary-foreground": "oklch(1.00 0 0)", "secondary": "oklch(0.50 0.09 126.19)", "secondary-foreground": "oklch(1.00 0 0)", "muted": "oklch(0.78 0 0)", "muted-foreground": "oklch(0.41 0 0)", "accent": "oklch(0.59 0.10 245.74)", "accent-foreground": "oklch(1.00 0 0)", "destructive": "oklch(0.71 0.20 46.46)", "destructive-foreground": "oklch(0 0 0)", "border": "oklch(0.43 0 0)", "input": "oklch(0.43 0 0)", "ring": "oklch(0.50 0.19 27.48)", "chart-1": "oklch(0.50 0.19 27.48)", "chart-2": "oklch(0.50 0.09 126.19)", "chart-3": "oklch(0.59 0.10 245.74)", "chart-4": "oklch(0.71 0.20 46.46)", "chart-5": "oklch(0.57 0.04 40.43)", "radius": "0px", "sidebar": "oklch(0.76 0 0)", "sidebar-foreground": "oklch(0.24 0 0)", "sidebar-primary": "oklch(0.50 0.19 27.48)", "sidebar-primary-foreground": "oklch(1.00 0 0)", "sidebar-accent": "oklch(0.59 0.10 245.74)", "sidebar-accent-foreground": "oklch(1.00 0 0)", "sidebar-border": "oklch(0.43 0 0)", "sidebar-ring": "oklch(0.50 0.19 27.48)", "font-sans": "\"Oxanium\", sans-serif", "font-serif": "ui-serif, Georgia, Cambria, \"Times New Roman\", Times, serif", "font-mono": "\"Source Code Pro\", monospace", "shadow-color": "hsl(0 0% 0%)", "shadow-opacity": "0.4", "shadow-blur": "4px", "shadow-spread": "0px", "shadow-offset-x": "0px", "shadow-offset-y": "2px", "letter-spacing": "0em", "spacing": "0.25rem", "shadow-2xs": "0px 2px 4px 0px hsl(0 0% 0% / 0.20)", "shadow-xs": "0px 2px 4px 0px hsl(0 0% 0% / 0.20)", "shadow-sm": "0px 2px 4px 0px hsl(0 0% 0% / 0.40), 0px 1px 2px -1px hsl(0 0% 0% / 0.40)", "shadow": "0px 2px 4px 0px hsl(0 0% 0% / 0.40), 0px 1px 2px -1px hsl(0 0% 0% / 0.40)", "shadow-md": "0px 2px 4px 0px hsl(0 0% 0% / 0.40), 0px 2px 4px -1px hsl(0 0% 0% / 0.40)", "shadow-lg": "0px 2px 4px 0px hsl(0 0% 0% / 0.40), 0px 4px 6px -1px hsl(0 0% 0% / 0.40)", "shadow-xl": "0px 2px 4px 0px hsl(0 0% 0% / 0.40), 0px 8px 10px -1px hsl(0 0% 0% / 0.40)", "shadow-2xl": "0px 2px 4px 0px hsl(0 0% 0% / 1.00)", "tracking-normal": "0em"}, "dark": {"background": "oklch(0.22 0 0)", "foreground": "oklch(0.91 0 0)", "card": "oklch(0.29 0 0)", "card-foreground": "oklch(0.91 0 0)", "popover": "oklch(0.29 0 0)", "popover-foreground": "oklch(0.91 0 0)", "primary": "oklch(0.61 0.21 27.03)", "primary-foreground": "oklch(1.00 0 0)", "secondary": "oklch(0.64 0.15 133.01)", "secondary-foreground": "oklch(0 0 0)", "muted": "oklch(0.26 0 0)", "muted-foreground": "oklch(0.71 0 0)", "accent": "oklch(0.75 0.12 244.75)", "accent-foreground": "oklch(0 0 0)", "destructive": "oklch(0.78 0.17 68.09)", "destructive-foreground": "oklch(0 0 0)", "border": "oklch(0.41 0 0)", "input": "oklch(0.41 0 0)", "ring": "oklch(0.61 0.21 27.03)", "chart-1": "oklch(0.61 0.21 27.03)", "chart-2": "oklch(0.64 0.15 133.01)", "chart-3": "oklch(0.75 0.12 244.75)", "chart-4": "oklch(0.78 0.17 68.09)", "chart-5": "oklch(0.65 0.03 40.80)", "radius": "0px", "sidebar": "oklch(0.19 0 0)", "sidebar-foreground": "oklch(0.91 0 0)", "sidebar-primary": "oklch(0.61 0.21 27.03)", "sidebar-primary-foreground": "oklch(1.00 0 0)", "sidebar-accent": "oklch(0.75 0.12 244.75)", "sidebar-accent-foreground": "oklch(0 0 0)", "sidebar-border": "oklch(0.41 0 0)", "sidebar-ring": "oklch(0.61 0.21 27.03)", "font-sans": "\"Oxanium\", sans-serif", "font-serif": "ui-serif, Georgia, Cambria, \"Times New Roman\", Times, serif", "font-mono": "\"Source Code Pro\", monospace", "shadow-color": "hsl(0 0% 0%)", "shadow-opacity": "0.6", "shadow-blur": "5px", "shadow-spread": "0px", "shadow-offset-x": "0px", "shadow-offset-y": "2px", "letter-spacing": "0em", "spacing": "0.25rem", "shadow-2xs": "0px 2px 5px 0px hsl(0 0% 0% / 0.30)", "shadow-xs": "0px 2px 5px 0px hsl(0 0% 0% / 0.30)", "shadow-sm": "0px 2px 5px 0px hsl(0 0% 0% / 0.60), 0px 1px 2px -1px hsl(0 0% 0% / 0.60)", "shadow": "0px 2px 5px 0px hsl(0 0% 0% / 0.60), 0px 1px 2px -1px hsl(0 0% 0% / 0.60)", "shadow-md": "0px 2px 5px 0px hsl(0 0% 0% / 0.60), 0px 2px 4px -1px hsl(0 0% 0% / 0.60)", "shadow-lg": "0px 2px 5px 0px hsl(0 0% 0% / 0.60), 0px 4px 6px -1px hsl(0 0% 0% / 0.60)", "shadow-xl": "0px 2px 5px 0px hsl(0 0% 0% / 0.60), 0px 8px 10px -1px hsl(0 0% 0% / 0.60)", "shadow-2xl": "0px 2px 5px 0px hsl(0 0% 0% / 1.50)"}}}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "registry:style", "title": "<PERSON><PERSON><PERSON><PERSON>", "description": "A theme based on the Catppuccin color palette.", "css": {"@layer base": {"body": {"letter-spacing": "var(--tracking-normal)"}}}, "cssVars": {"theme": {"font-sans": "Montserrat, sans-serif", "font-mono": "Fira Code, monospace", "font-serif": "Georgia, serif", "radius": "0.35rem", "tracking-tighter": "calc(var(--tracking-normal) - 0.05em)", "tracking-tight": "calc(var(--tracking-normal) - 0.025em)", "tracking-wide": "calc(var(--tracking-normal) + 0.025em)", "tracking-wider": "calc(var(--tracking-normal) + 0.05em)", "tracking-widest": "calc(var(--tracking-normal) + 0.1em)"}, "light": {"background": "oklch(0.96 0.01 264.53)", "foreground": "oklch(0.44 0.04 279.33)", "card": "oklch(1.00 0 0)", "card-foreground": "oklch(0.44 0.04 279.33)", "popover": "oklch(0.86 0.01 268.48)", "popover-foreground": "oklch(0.44 0.04 279.33)", "primary": "oklch(0.55 0.25 297.02)", "primary-foreground": "oklch(1.00 0 0)", "secondary": "oklch(0.86 0.01 268.48)", "secondary-foreground": "oklch(0.44 0.04 279.33)", "muted": "oklch(0.91 0.01 264.51)", "muted-foreground": "oklch(0.55 0.03 279.08)", "accent": "oklch(0.68 0.14 235.38)", "accent-foreground": "oklch(1.00 0 0)", "destructive": "oklch(0.55 0.22 19.81)", "destructive-foreground": "oklch(1.00 0 0)", "border": "oklch(0.81 0.02 271.20)", "input": "oklch(0.86 0.01 268.48)", "ring": "oklch(0.55 0.25 297.02)", "chart-1": "oklch(0.55 0.25 297.02)", "chart-2": "oklch(0.68 0.14 235.38)", "chart-3": "oklch(0.63 0.18 140.44)", "chart-4": "oklch(0.69 0.20 42.43)", "chart-5": "oklch(0.71 0.10 33.10)", "radius": "0.35rem", "sidebar": "oklch(0.93 0.01 264.52)", "sidebar-foreground": "oklch(0.44 0.04 279.33)", "sidebar-primary": "oklch(0.55 0.25 297.02)", "sidebar-primary-foreground": "oklch(1.00 0 0)", "sidebar-accent": "oklch(0.68 0.14 235.38)", "sidebar-accent-foreground": "oklch(1.00 0 0)", "sidebar-border": "oklch(0.81 0.02 271.20)", "sidebar-ring": "oklch(0.55 0.25 297.02)", "font-sans": "Montserrat, sans-serif", "font-serif": "Georgia, serif", "font-mono": "Fira Code, monospace", "shadow-color": "hsl(240 30% 25%)", "shadow-opacity": "0.12", "shadow-blur": "6px", "shadow-spread": "0px", "shadow-offset-x": "0px", "shadow-offset-y": "4px", "letter-spacing": "0em", "spacing": "0.25rem", "shadow-2xs": "0px 4px 6px 0px hsl(240 30% 25% / 0.06)", "shadow-xs": "0px 4px 6px 0px hsl(240 30% 25% / 0.06)", "shadow-sm": "0px 4px 6px 0px hsl(240 30% 25% / 0.12), 0px 1px 2px -1px hsl(240 30% 25% / 0.12)", "shadow": "0px 4px 6px 0px hsl(240 30% 25% / 0.12), 0px 1px 2px -1px hsl(240 30% 25% / 0.12)", "shadow-md": "0px 4px 6px 0px hsl(240 30% 25% / 0.12), 0px 2px 4px -1px hsl(240 30% 25% / 0.12)", "shadow-lg": "0px 4px 6px 0px hsl(240 30% 25% / 0.12), 0px 4px 6px -1px hsl(240 30% 25% / 0.12)", "shadow-xl": "0px 4px 6px 0px hsl(240 30% 25% / 0.12), 0px 8px 10px -1px hsl(240 30% 25% / 0.12)", "shadow-2xl": "0px 4px 6px 0px hsl(240 30% 25% / 0.30)", "tracking-normal": "0em"}, "dark": {"background": "oklch(0.22 0.03 284.06)", "foreground": "oklch(0.88 0.04 272.28)", "card": "oklch(0.24 0.03 283.91)", "card-foreground": "oklch(0.88 0.04 272.28)", "popover": "oklch(0.40 0.03 280.15)", "popover-foreground": "oklch(0.88 0.04 272.28)", "primary": "oklch(0.79 0.12 304.77)", "primary-foreground": "oklch(0.24 0.03 283.91)", "secondary": "oklch(0.48 0.03 278.64)", "secondary-foreground": "oklch(0.88 0.04 272.28)", "muted": "oklch(0.30 0.03 276.21)", "muted-foreground": "oklch(0.75 0.04 273.93)", "accent": "oklch(0.85 0.08 210.25)", "accent-foreground": "oklch(0.24 0.03 283.91)", "destructive": "oklch(0.76 0.13 2.76)", "destructive-foreground": "oklch(0.24 0.03 283.91)", "border": "oklch(0.32 0.03 281.98)", "input": "oklch(0.32 0.03 281.98)", "ring": "oklch(0.79 0.12 304.77)", "chart-1": "oklch(0.79 0.12 304.77)", "chart-2": "oklch(0.85 0.08 210.25)", "chart-3": "oklch(0.86 0.11 142.72)", "chart-4": "oklch(0.82 0.10 52.63)", "chart-5": "oklch(0.92 0.02 30.49)", "radius": "0.35rem", "sidebar": "oklch(0.18 0.02 284.20)", "sidebar-foreground": "oklch(0.88 0.04 272.28)", "sidebar-primary": "oklch(0.79 0.12 304.77)", "sidebar-primary-foreground": "oklch(0.24 0.03 283.91)", "sidebar-accent": "oklch(0.85 0.08 210.25)", "sidebar-accent-foreground": "oklch(0.24 0.03 283.91)", "sidebar-border": "oklch(0.40 0.03 280.15)", "sidebar-ring": "oklch(0.79 0.12 304.77)", "font-sans": "Montserrat, sans-serif", "font-serif": "Georgia, serif", "font-mono": "Fira Code, monospace", "shadow-color": "hsl(240 30% 25%)", "shadow-opacity": "0.12", "shadow-blur": "6px", "shadow-spread": "0px", "shadow-offset-x": "0px", "shadow-offset-y": "4px", "letter-spacing": "0em", "spacing": "0.25rem", "shadow-2xs": "0px 4px 6px 0px hsl(240 30% 25% / 0.06)", "shadow-xs": "0px 4px 6px 0px hsl(240 30% 25% / 0.06)", "shadow-sm": "0px 4px 6px 0px hsl(240 30% 25% / 0.12), 0px 1px 2px -1px hsl(240 30% 25% / 0.12)", "shadow": "0px 4px 6px 0px hsl(240 30% 25% / 0.12), 0px 1px 2px -1px hsl(240 30% 25% / 0.12)", "shadow-md": "0px 4px 6px 0px hsl(240 30% 25% / 0.12), 0px 2px 4px -1px hsl(240 30% 25% / 0.12)", "shadow-lg": "0px 4px 6px 0px hsl(240 30% 25% / 0.12), 0px 4px 6px -1px hsl(240 30% 25% / 0.12)", "shadow-xl": "0px 4px 6px 0px hsl(240 30% 25% / 0.12), 0px 8px 10px -1px hsl(240 30% 25% / 0.12)", "shadow-2xl": "0px 4px 6px 0px hsl(240 30% 25% / 0.30)"}}}, {"name": "graphite", "type": "registry:style", "title": "<PERSON><PERSON><PERSON><PERSON>", "description": "A theme based on the Graphite color palette.", "css": {"@layer base": {"body": {"letter-spacing": "var(--tracking-normal)"}}}, "cssVars": {"theme": {"font-sans": "Inter, sans-serif", "font-mono": "Fira Code, monospace", "font-serif": "Georgia, serif", "radius": "0.35rem", "tracking-tighter": "calc(var(--tracking-normal) - 0.05em)", "tracking-tight": "calc(var(--tracking-normal) - 0.025em)", "tracking-wide": "calc(var(--tracking-normal) + 0.025em)", "tracking-wider": "calc(var(--tracking-normal) + 0.05em)", "tracking-widest": "calc(var(--tracking-normal) + 0.1em)"}, "light": {"background": "oklch(0.96 0 0)", "foreground": "oklch(0.32 0 0)", "card": "oklch(0.97 0 0)", "card-foreground": "oklch(0.32 0 0)", "popover": "oklch(0.97 0 0)", "popover-foreground": "oklch(0.32 0 0)", "primary": "oklch(0.49 0 0)", "primary-foreground": "oklch(1.00 0 0)", "secondary": "oklch(0.91 0 0)", "secondary-foreground": "oklch(0.32 0 0)", "muted": "oklch(0.89 0 0)", "muted-foreground": "oklch(0.51 0 0)", "accent": "oklch(0.81 0 0)", "accent-foreground": "oklch(0.32 0 0)", "destructive": "oklch(0.56 0.19 25.86)", "destructive-foreground": "oklch(1.00 0 0)", "border": "oklch(0.86 0 0)", "input": "oklch(0.91 0 0)", "ring": "oklch(0.49 0 0)", "chart-1": "oklch(0.49 0 0)", "chart-2": "oklch(0.49 0.04 196.03)", "chart-3": "oklch(0.65 0 0)", "chart-4": "oklch(0.73 0 0)", "chart-5": "oklch(0.81 0 0)", "radius": "0.35rem", "sidebar": "oklch(0.94 0 0)", "sidebar-foreground": "oklch(0.32 0 0)", "sidebar-primary": "oklch(0.49 0 0)", "sidebar-primary-foreground": "oklch(1.00 0 0)", "sidebar-accent": "oklch(0.81 0 0)", "sidebar-accent-foreground": "oklch(0.32 0 0)", "sidebar-border": "oklch(0.86 0 0)", "sidebar-ring": "oklch(0.49 0 0)", "font-sans": "Montserrat, sans-serif", "font-serif": "Georgia, serif", "font-mono": "Fira Code, monospace", "shadow-color": "hsl(0 0% 20% / 0.1)", "shadow-opacity": "0.15", "shadow-blur": "0px", "shadow-spread": "0px", "shadow-offset-x": "0px", "shadow-offset-y": "2px", "letter-spacing": "0em", "spacing": "0.25rem", "shadow-2xs": "0px 2px 0px 0px hsl(0 0% 20% / 0.07)", "shadow-xs": "0px 2px 0px 0px hsl(0 0% 20% / 0.07)", "shadow-sm": "0px 2px 0px 0px hsl(0 0% 20% / 0.15), 0px 1px 2px -1px hsl(0 0% 20% / 0.15)", "shadow": "0px 2px 0px 0px hsl(0 0% 20% / 0.15), 0px 1px 2px -1px hsl(0 0% 20% / 0.15)", "shadow-md": "0px 2px 0px 0px hsl(0 0% 20% / 0.15), 0px 2px 4px -1px hsl(0 0% 20% / 0.15)", "shadow-lg": "0px 2px 0px 0px hsl(0 0% 20% / 0.15), 0px 4px 6px -1px hsl(0 0% 20% / 0.15)", "shadow-xl": "0px 2px 0px 0px hsl(0 0% 20% / 0.15), 0px 8px 10px -1px hsl(0 0% 20% / 0.15)", "shadow-2xl": "0px 2px 0px 0px hsl(0 0% 20% / 0.38)", "tracking-normal": "0em"}, "dark": {"background": "oklch(0.22 0 0)", "foreground": "oklch(0.89 0 0)", "card": "oklch(0.24 0 0)", "card-foreground": "oklch(0.89 0 0)", "popover": "oklch(0.24 0 0)", "popover-foreground": "oklch(0.89 0 0)", "primary": "oklch(0.71 0 0)", "primary-foreground": "oklch(0.22 0 0)", "secondary": "oklch(0.31 0 0)", "secondary-foreground": "oklch(0.89 0 0)", "muted": "oklch(0.29 0 0)", "muted-foreground": "oklch(0.60 0 0)", "accent": "oklch(0.37 0 0)", "accent-foreground": "oklch(0.89 0 0)", "destructive": "oklch(0.66 0.15 22.17)", "destructive-foreground": "oklch(1.00 0 0)", "border": "oklch(0.33 0 0)", "input": "oklch(0.31 0 0)", "ring": "oklch(0.71 0 0)", "chart-1": "oklch(0.71 0 0)", "chart-2": "oklch(0.67 0.03 206.35)", "chart-3": "oklch(0.55 0 0)", "chart-4": "oklch(0.46 0 0)", "chart-5": "oklch(0.37 0 0)", "radius": "0.35rem", "sidebar": "oklch(0.24 0 0)", "sidebar-foreground": "oklch(0.89 0 0)", "sidebar-primary": "oklch(0.71 0 0)", "sidebar-primary-foreground": "oklch(0.22 0 0)", "sidebar-accent": "oklch(0.37 0 0)", "sidebar-accent-foreground": "oklch(0.89 0 0)", "sidebar-border": "oklch(0.33 0 0)", "sidebar-ring": "oklch(0.71 0 0)", "font-sans": "Inter, sans-serif", "font-serif": "Georgia, serif", "font-mono": "Fira Code, monospace", "shadow-color": "hsl(0 0% 20% / 0.1)", "shadow-opacity": "0.15", "shadow-blur": "0px", "shadow-spread": "0px", "shadow-offset-x": "0px", "shadow-offset-y": "2px", "letter-spacing": "0em", "spacing": "0.25rem", "shadow-2xs": "0px 2px 0px 0px hsl(0 0% 20% / 0.07)", "shadow-xs": "0px 2px 0px 0px hsl(0 0% 20% / 0.07)", "shadow-sm": "0px 2px 0px 0px hsl(0 0% 20% / 0.15), 0px 1px 2px -1px hsl(0 0% 20% / 0.15)", "shadow": "0px 2px 0px 0px hsl(0 0% 20% / 0.15), 0px 1px 2px -1px hsl(0 0% 20% / 0.15)", "shadow-md": "0px 2px 0px 0px hsl(0 0% 20% / 0.15), 0px 2px 4px -1px hsl(0 0% 20% / 0.15)", "shadow-lg": "0px 2px 0px 0px hsl(0 0% 20% / 0.15), 0px 4px 6px -1px hsl(0 0% 20% / 0.15)", "shadow-xl": "0px 2px 0px 0px hsl(0 0% 20% / 0.15), 0px 8px 10px -1px hsl(0 0% 20% / 0.15)", "shadow-2xl": "0px 2px 0px 0px hsl(0 0% 20% / 0.38)"}}}, {"name": "perpetuity", "type": "registry:style", "title": "Perpetuity", "description": "A theme based on the Perpetuity color palette.", "css": {"@layer base": {"body": {"letter-spacing": "var(--tracking-normal)"}}}, "cssVars": {"theme": {"font-sans": "Source Code Pro, monospace", "font-mono": "Source Code Pro, monospace", "font-serif": "Source Code Pro, monospace", "radius": "0.125rem", "tracking-tighter": "calc(var(--tracking-normal) - 0.05em)", "tracking-tight": "calc(var(--tracking-normal) - 0.025em)", "tracking-wide": "calc(var(--tracking-normal) + 0.025em)", "tracking-wider": "calc(var(--tracking-normal) + 0.05em)", "tracking-widest": "calc(var(--tracking-normal) + 0.1em)"}, "light": {"background": "oklch(0.95 0.01 197.01)", "foreground": "oklch(0.38 0.06 212.66)", "card": "oklch(0.97 0.01 197.07)", "card-foreground": "oklch(0.38 0.06 212.66)", "popover": "oklch(0.97 0.01 197.07)", "popover-foreground": "oklch(0.38 0.06 212.66)", "primary": "oklch(0.56 0.09 203.28)", "primary-foreground": "oklch(1.00 0 0)", "secondary": "oklch(0.92 0.02 196.84)", "secondary-foreground": "oklch(0.38 0.06 212.66)", "muted": "oklch(0.93 0.01 196.97)", "muted-foreground": "oklch(0.54 0.06 201.57)", "accent": "oklch(0.90 0.03 201.89)", "accent-foreground": "oklch(0.38 0.06 212.66)", "destructive": "oklch(0.57 0.19 25.54)", "destructive-foreground": "oklch(1.00 0 0)", "border": "oklch(0.89 0.02 204.41)", "input": "oklch(0.92 0.02 196.84)", "ring": "oklch(0.56 0.09 203.28)", "chart-1": "oklch(0.56 0.09 203.28)", "chart-2": "oklch(0.64 0.10 201.59)", "chart-3": "oklch(0.71 0.11 201.25)", "chart-4": "oklch(0.77 0.10 201.18)", "chart-5": "oklch(0.83 0.08 200.97)", "radius": "0.125rem", "sidebar": "oklch(0.93 0.02 205.32)", "sidebar-foreground": "oklch(0.38 0.06 212.66)", "sidebar-primary": "oklch(0.56 0.09 203.28)", "sidebar-primary-foreground": "oklch(1.00 0 0)", "sidebar-accent": "oklch(0.90 0.03 201.89)", "sidebar-accent-foreground": "oklch(0.38 0.06 212.66)", "sidebar-border": "oklch(0.89 0.02 204.41)", "sidebar-ring": "oklch(0.56 0.09 203.28)", "font-sans": "Courier New, monospace", "font-serif": "Courier New, monospace", "font-mono": "Courier New, monospace", "shadow-color": "hsl(185 70% 30% / 0.15)", "shadow-opacity": "0.15", "shadow-blur": "2px", "shadow-spread": "0px", "shadow-offset-x": "1px", "shadow-offset-y": "1px", "letter-spacing": "0em", "spacing": "0.25rem", "shadow-2xs": "1px 1px 2px 0px hsl(185 70% 30% / 0.07)", "shadow-xs": "1px 1px 2px 0px hsl(185 70% 30% / 0.07)", "shadow-sm": "1px 1px 2px 0px hsl(185 70% 30% / 0.15), 1px 1px 2px -1px hsl(185 70% 30% / 0.15)", "shadow": "1px 1px 2px 0px hsl(185 70% 30% / 0.15), 1px 1px 2px -1px hsl(185 70% 30% / 0.15)", "shadow-md": "1px 1px 2px 0px hsl(185 70% 30% / 0.15), 1px 2px 4px -1px hsl(185 70% 30% / 0.15)", "shadow-lg": "1px 1px 2px 0px hsl(185 70% 30% / 0.15), 1px 4px 6px -1px hsl(185 70% 30% / 0.15)", "shadow-xl": "1px 1px 2px 0px hsl(185 70% 30% / 0.15), 1px 8px 10px -1px hsl(185 70% 30% / 0.15)", "shadow-2xl": "1px 1px 2px 0px hsl(185 70% 30% / 0.38)", "tracking-normal": "0em"}, "dark": {"background": "oklch(0.21 0.02 224.45)", "foreground": "oklch(0.85 0.13 195.04)", "card": "oklch(0.23 0.03 216.07)", "card-foreground": "oklch(0.85 0.13 195.04)", "popover": "oklch(0.23 0.03 216.07)", "popover-foreground": "oklch(0.85 0.13 195.04)", "primary": "oklch(0.85 0.13 195.04)", "primary-foreground": "oklch(0.21 0.02 224.45)", "secondary": "oklch(0.38 0.06 216.50)", "secondary-foreground": "oklch(0.85 0.13 195.04)", "muted": "oklch(0.29 0.04 218.82)", "muted-foreground": "oklch(0.66 0.10 195.05)", "accent": "oklch(0.38 0.06 216.50)", "accent-foreground": "oklch(0.85 0.13 195.04)", "destructive": "oklch(0.62 0.21 25.81)", "destructive-foreground": "oklch(0.96 0 0)", "border": "oklch(0.38 0.06 216.50)", "input": "oklch(0.38 0.06 216.50)", "ring": "oklch(0.85 0.13 195.04)", "chart-1": "oklch(0.85 0.13 195.04)", "chart-2": "oklch(0.66 0.10 195.05)", "chart-3": "oklch(0.58 0.08 195.07)", "chart-4": "oklch(0.43 0.06 202.62)", "chart-5": "oklch(0.31 0.05 204.16)", "radius": "0.125rem", "sidebar": "oklch(0.21 0.02 224.45)", "sidebar-foreground": "oklch(0.85 0.13 195.04)", "sidebar-primary": "oklch(0.85 0.13 195.04)", "sidebar-primary-foreground": "oklch(0.21 0.02 224.45)", "sidebar-accent": "oklch(0.38 0.06 216.50)", "sidebar-accent-foreground": "oklch(0.85 0.13 195.04)", "sidebar-border": "oklch(0.38 0.06 216.50)", "sidebar-ring": "oklch(0.85 0.13 195.04)", "font-sans": "Source Code Pro, monospace", "font-serif": "Source Code Pro, monospace", "font-mono": "Source Code Pro, monospace", "shadow-color": "hsl(180 70% 60% / 0.2)", "shadow-opacity": "0.2", "shadow-blur": "2px", "shadow-spread": "0px", "shadow-offset-x": "1px", "shadow-offset-y": "1px", "letter-spacing": "0em", "spacing": "0.25rem", "shadow-2xs": "1px 1px 2px 0px hsl(180 70% 60% / 0.10)", "shadow-xs": "1px 1px 2px 0px hsl(180 70% 60% / 0.10)", "shadow-sm": "1px 1px 2px 0px hsl(180 70% 60% / 0.20), 1px 1px 2px -1px hsl(180 70% 60% / 0.20)", "shadow": "1px 1px 2px 0px hsl(180 70% 60% / 0.20), 1px 1px 2px -1px hsl(180 70% 60% / 0.20)", "shadow-md": "1px 1px 2px 0px hsl(180 70% 60% / 0.20), 1px 2px 4px -1px hsl(180 70% 60% / 0.20)", "shadow-lg": "1px 1px 2px 0px hsl(180 70% 60% / 0.20), 1px 4px 6px -1px hsl(180 70% 60% / 0.20)", "shadow-xl": "1px 1px 2px 0px hsl(180 70% 60% / 0.20), 1px 8px 10px -1px hsl(180 70% 60% / 0.20)", "shadow-2xl": "1px 1px 2px 0px hsl(180 70% 60% / 0.50)"}}}, {"name": "kodama-grove", "type": "registry:style", "title": "Kodama <PERSON>", "description": "A theme based on the Kodama Grove color palette.", "css": {"@layer base": {"body": {"letter-spacing": "var(--tracking-normal)"}}}, "cssVars": {"theme": {"font-sans": "<PERSON><PERSON><PERSON><PERSON>, serif", "font-mono": "JetBrains Mono, monospace", "font-serif": "Source Serif 4, serif", "radius": "0.425rem", "tracking-tighter": "calc(var(--tracking-normal) - 0.05em)", "tracking-tight": "calc(var(--tracking-normal) - 0.025em)", "tracking-wide": "calc(var(--tracking-normal) + 0.025em)", "tracking-wider": "calc(var(--tracking-normal) + 0.05em)", "tracking-widest": "calc(var(--tracking-normal) + 0.1em)"}, "light": {"background": "oklch(0.88 0.05 91.79)", "foreground": "oklch(0.43 0.03 59.22)", "card": "oklch(0.89 0.04 87.57)", "card-foreground": "oklch(0.43 0.03 59.22)", "popover": "oklch(0.94 0.03 89.85)", "popover-foreground": "oklch(0.43 0.03 59.22)", "primary": "oklch(0.67 0.11 118.91)", "primary-foreground": "oklch(0.99 0.01 88.64)", "secondary": "oklch(0.85 0.06 91.15)", "secondary-foreground": "oklch(0.43 0.03 59.22)", "muted": "oklch(0.85 0.06 91.15)", "muted-foreground": "oklch(0.58 0.03 60.93)", "accent": "oklch(0.84 0.07 90.33)", "accent-foreground": "oklch(0.43 0.03 59.22)", "destructive": "oklch(0.71 0.10 29.98)", "destructive-foreground": "oklch(0.98 0.01 91.48)", "border": "oklch(0.69 0.04 59.84)", "input": "oklch(0.84 0.07 90.33)", "ring": "oklch(0.73 0.06 130.85)", "chart-1": "oklch(0.73 0.06 130.85)", "chart-2": "oklch(0.68 0.06 132.45)", "chart-3": "oklch(0.82 0.03 136.65)", "chart-4": "oklch(0.59 0.05 137.62)", "chart-5": "oklch(0.52 0.04 137.19)", "radius": "0.425rem", "sidebar": "oklch(0.86 0.06 90.52)", "sidebar-foreground": "oklch(0.43 0.03 59.22)", "sidebar-primary": "oklch(0.73 0.06 130.85)", "sidebar-primary-foreground": "oklch(0.99 0.01 88.64)", "sidebar-accent": "oklch(0.92 0.02 88.00)", "sidebar-accent-foreground": "oklch(0.43 0.03 59.22)", "sidebar-border": "oklch(0.91 0.02 88.00)", "sidebar-ring": "oklch(0.73 0.06 130.85)", "font-sans": "<PERSON><PERSON><PERSON><PERSON>, serif", "font-serif": "Source Serif 4, serif", "font-mono": "JetBrains Mono, monospace", "shadow-color": "hsl(88 22% 35% / 0.15)", "shadow-opacity": "0.15", "shadow-blur": "2px", "shadow-spread": "0px", "shadow-offset-x": "3px", "shadow-offset-y": "3px", "letter-spacing": "0em", "spacing": "0.25rem", "shadow-2xs": "3px 3px 2px 0px hsl(88 22% 35% / 0.07)", "shadow-xs": "3px 3px 2px 0px hsl(88 22% 35% / 0.07)", "shadow-sm": "3px 3px 2px 0px hsl(88 22% 35% / 0.15), 3px 1px 2px -1px hsl(88 22% 35% / 0.15)", "shadow": "3px 3px 2px 0px hsl(88 22% 35% / 0.15), 3px 1px 2px -1px hsl(88 22% 35% / 0.15)", "shadow-md": "3px 3px 2px 0px hsl(88 22% 35% / 0.15), 3px 2px 4px -1px hsl(88 22% 35% / 0.15)", "shadow-lg": "3px 3px 2px 0px hsl(88 22% 35% / 0.15), 3px 4px 6px -1px hsl(88 22% 35% / 0.15)", "shadow-xl": "3px 3px 2px 0px hsl(88 22% 35% / 0.15), 3px 8px 10px -1px hsl(88 22% 35% / 0.15)", "shadow-2xl": "3px 3px 2px 0px hsl(88 22% 35% / 0.38)", "tracking-normal": "0em"}, "dark": {"background": "oklch(0.33 0.02 88.07)", "foreground": "oklch(0.92 0.02 82.12)", "card": "oklch(0.36 0.02 82.33)", "card-foreground": "oklch(0.92 0.02 82.12)", "popover": "oklch(0.36 0.02 82.33)", "popover-foreground": "oklch(0.92 0.02 82.12)", "primary": "oklch(0.68 0.06 132.45)", "primary-foreground": "oklch(0.27 0.01 61.02)", "secondary": "oklch(0.44 0.02 84.55)", "secondary-foreground": "oklch(0.92 0.02 82.12)", "muted": "oklch(0.39 0.02 82.71)", "muted-foreground": "oklch(0.71 0.02 73.62)", "accent": "oklch(0.65 0.07 90.76)", "accent-foreground": "oklch(0.27 0.01 61.02)", "destructive": "oklch(0.63 0.08 31.30)", "destructive-foreground": "oklch(0.94 0.02 84.59)", "border": "oklch(0.44 0.02 84.55)", "input": "oklch(0.44 0.02 84.55)", "ring": "oklch(0.68 0.06 132.45)", "chart-1": "oklch(0.68 0.06 132.45)", "chart-2": "oklch(0.73 0.06 130.85)", "chart-3": "oklch(0.59 0.05 137.62)", "chart-4": "oklch(0.65 0.07 90.76)", "chart-5": "oklch(0.52 0.04 137.19)", "radius": "0.425rem", "sidebar": "oklch(0.33 0.02 88.07)", "sidebar-foreground": "oklch(0.92 0.02 82.12)", "sidebar-primary": "oklch(0.68 0.06 132.45)", "sidebar-primary-foreground": "oklch(0.27 0.01 61.02)", "sidebar-accent": "oklch(0.65 0.07 90.76)", "sidebar-accent-foreground": "oklch(0.27 0.01 61.02)", "sidebar-border": "oklch(0.44 0.02 84.55)", "sidebar-ring": "oklch(0.68 0.06 132.45)", "font-sans": "<PERSON><PERSON><PERSON><PERSON>, serif", "font-serif": "Source Serif 4, serif", "font-mono": "JetBrains Mono, monospace", "shadow-color": "hsl(88 22% 35% / 0.15)", "shadow-opacity": "0.15", "shadow-blur": "2px", "shadow-spread": "0px", "shadow-offset-x": "3px", "shadow-offset-y": "3px", "letter-spacing": "0em", "spacing": "0.25rem", "shadow-2xs": "3px 3px 2px 0px hsl(88 22% 35% / 0.07)", "shadow-xs": "3px 3px 2px 0px hsl(88 22% 35% / 0.07)", "shadow-sm": "3px 3px 2px 0px hsl(88 22% 35% / 0.15), 3px 1px 2px -1px hsl(88 22% 35% / 0.15)", "shadow": "3px 3px 2px 0px hsl(88 22% 35% / 0.15), 3px 1px 2px -1px hsl(88 22% 35% / 0.15)", "shadow-md": "3px 3px 2px 0px hsl(88 22% 35% / 0.15), 3px 2px 4px -1px hsl(88 22% 35% / 0.15)", "shadow-lg": "3px 3px 2px 0px hsl(88 22% 35% / 0.15), 3px 4px 6px -1px hsl(88 22% 35% / 0.15)", "shadow-xl": "3px 3px 2px 0px hsl(88 22% 35% / 0.15), 3px 8px 10px -1px hsl(88 22% 35% / 0.15)", "shadow-2xl": "3px 3px 2px 0px hsl(88 22% 35% / 0.38)"}}}, {"name": "cosmic-night", "type": "registry:style", "title": "Cosmic Night", "description": "A theme based on the Cosmic Night color palette.", "css": {"@layer base": {"body": {"letter-spacing": "var(--tracking-normal)"}}}, "cssVars": {"theme": {"font-sans": "Inter, sans-serif", "font-mono": "JetBrains Mono, monospace", "font-serif": "Georgia, serif", "radius": "0.5rem", "tracking-tighter": "calc(var(--tracking-normal) - 0.05em)", "tracking-tight": "calc(var(--tracking-normal) - 0.025em)", "tracking-wide": "calc(var(--tracking-normal) + 0.025em)", "tracking-wider": "calc(var(--tracking-normal) + 0.05em)", "tracking-widest": "calc(var(--tracking-normal) + 0.1em)"}, "light": {"background": "oklch(0.97 0.01 286.15)", "foreground": "oklch(0.30 0.06 282.42)", "card": "oklch(1.00 0 0)", "card-foreground": "oklch(0.30 0.06 282.42)", "popover": "oklch(1.00 0 0)", "popover-foreground": "oklch(0.30 0.06 282.42)", "primary": "oklch(0.54 0.18 288.03)", "primary-foreground": "oklch(1.00 0 0)", "secondary": "oklch(0.92 0.04 292.69)", "secondary-foreground": "oklch(0.41 0.10 288.17)", "muted": "oklch(0.96 0.01 286.15)", "muted-foreground": "oklch(0.54 0.05 284.74)", "accent": "oklch(0.92 0.04 262.14)", "accent-foreground": "oklch(0.30 0.06 282.42)", "destructive": "oklch(0.69 0.21 14.99)", "destructive-foreground": "oklch(1.00 0 0)", "border": "oklch(0.91 0.02 285.96)", "input": "oklch(0.91 0.02 285.96)", "ring": "oklch(0.54 0.18 288.03)", "chart-1": "oklch(0.54 0.18 288.03)", "chart-2": "oklch(0.70 0.16 288.99)", "chart-3": "oklch(0.57 0.21 276.71)", "chart-4": "oklch(0.64 0.19 281.81)", "chart-5": "oklch(0.45 0.18 279.38)", "radius": "0.5rem", "sidebar": "oklch(0.96 0.01 286.15)", "sidebar-foreground": "oklch(0.30 0.06 282.42)", "sidebar-primary": "oklch(0.54 0.18 288.03)", "sidebar-primary-foreground": "oklch(1.00 0 0)", "sidebar-accent": "oklch(0.92 0.04 262.14)", "sidebar-accent-foreground": "oklch(0.30 0.06 282.42)", "sidebar-border": "oklch(0.91 0.02 285.96)", "sidebar-ring": "oklch(0.54 0.18 288.03)", "font-sans": "Inter, sans-serif", "font-serif": "Georgia, serif", "font-mono": "JetBrains Mono, monospace", "shadow-color": "hsl(240 30% 25%)", "shadow-opacity": "0.12", "shadow-blur": "10px", "shadow-spread": "0px", "shadow-offset-x": "0px", "shadow-offset-y": "4px", "letter-spacing": "0em", "spacing": "0.25rem", "shadow-2xs": "0px 4px 10px 0px hsl(240 30% 25% / 0.06)", "shadow-xs": "0px 4px 10px 0px hsl(240 30% 25% / 0.06)", "shadow-sm": "0px 4px 10px 0px hsl(240 30% 25% / 0.12), 0px 1px 2px -1px hsl(240 30% 25% / 0.12)", "shadow": "0px 4px 10px 0px hsl(240 30% 25% / 0.12), 0px 1px 2px -1px hsl(240 30% 25% / 0.12)", "shadow-md": "0px 4px 10px 0px hsl(240 30% 25% / 0.12), 0px 2px 4px -1px hsl(240 30% 25% / 0.12)", "shadow-lg": "0px 4px 10px 0px hsl(240 30% 25% / 0.12), 0px 4px 6px -1px hsl(240 30% 25% / 0.12)", "shadow-xl": "0px 4px 10px 0px hsl(240 30% 25% / 0.12), 0px 8px 10px -1px hsl(240 30% 25% / 0.12)", "shadow-2xl": "0px 4px 10px 0px hsl(240 30% 25% / 0.30)", "tracking-normal": "0em"}, "dark": {"background": "oklch(0.17 0.02 283.80)", "foreground": "oklch(0.92 0.03 285.88)", "card": "oklch(0.23 0.04 282.93)", "card-foreground": "oklch(0.92 0.03 285.88)", "popover": "oklch(0.23 0.04 282.93)", "popover-foreground": "oklch(0.92 0.03 285.88)", "primary": "oklch(0.72 0.16 290.40)", "primary-foreground": "oklch(0.17 0.02 283.80)", "secondary": "oklch(0.31 0.07 283.46)", "secondary-foreground": "oklch(0.84 0.08 285.91)", "muted": "oklch(0.27 0.06 281.44)", "muted-foreground": "oklch(0.72 0.05 285.17)", "accent": "oklch(0.34 0.08 280.97)", "accent-foreground": "oklch(0.92 0.03 285.88)", "destructive": "oklch(0.69 0.21 14.99)", "destructive-foreground": "oklch(1.00 0 0)", "border": "oklch(0.33 0.06 282.58)", "input": "oklch(0.33 0.06 282.58)", "ring": "oklch(0.72 0.16 290.40)", "chart-1": "oklch(0.72 0.16 290.40)", "chart-2": "oklch(0.64 0.10 274.91)", "chart-3": "oklch(0.75 0.12 244.75)", "chart-4": "oklch(0.71 0.10 186.68)", "chart-5": "oklch(0.75 0.18 346.81)", "radius": "0.5rem", "sidebar": "oklch(0.23 0.04 282.93)", "sidebar-foreground": "oklch(0.92 0.03 285.88)", "sidebar-primary": "oklch(0.72 0.16 290.40)", "sidebar-primary-foreground": "oklch(0.17 0.02 283.80)", "sidebar-accent": "oklch(0.34 0.08 280.97)", "sidebar-accent-foreground": "oklch(0.92 0.03 285.88)", "sidebar-border": "oklch(0.33 0.06 282.58)", "sidebar-ring": "oklch(0.72 0.16 290.40)", "font-sans": "Inter, sans-serif", "font-serif": "Georgia, serif", "font-mono": "JetBrains Mono, monospace", "shadow-color": "hsl(240 30% 25%)", "shadow-opacity": "0.12", "shadow-blur": "10px", "shadow-spread": "0px", "shadow-offset-x": "0px", "shadow-offset-y": "4px", "letter-spacing": "0em", "spacing": "0.25rem", "shadow-2xs": "0px 4px 10px 0px hsl(240 30% 25% / 0.06)", "shadow-xs": "0px 4px 10px 0px hsl(240 30% 25% / 0.06)", "shadow-sm": "0px 4px 10px 0px hsl(240 30% 25% / 0.12), 0px 1px 2px -1px hsl(240 30% 25% / 0.12)", "shadow": "0px 4px 10px 0px hsl(240 30% 25% / 0.12), 0px 1px 2px -1px hsl(240 30% 25% / 0.12)", "shadow-md": "0px 4px 10px 0px hsl(240 30% 25% / 0.12), 0px 2px 4px -1px hsl(240 30% 25% / 0.12)", "shadow-lg": "0px 4px 10px 0px hsl(240 30% 25% / 0.12), 0px 4px 6px -1px hsl(240 30% 25% / 0.12)", "shadow-xl": "0px 4px 10px 0px hsl(240 30% 25% / 0.12), 0px 8px 10px -1px hsl(240 30% 25% / 0.12)", "shadow-2xl": "0px 4px 10px 0px hsl(240 30% 25% / 0.30)"}}}, {"name": "tangerine", "type": "registry:style", "title": "Tangerine", "description": "A theme based on the Tangerine color palette.", "css": {"@layer base": {"body": {"letter-spacing": "var(--tracking-normal)"}}}, "cssVars": {"theme": {"font-sans": "Inter, sans-serif", "font-mono": "JetBrains Mono, monospace", "font-serif": "Source Serif 4, serif", "radius": "0.75rem", "tracking-tighter": "calc(var(--tracking-normal) - 0.05em)", "tracking-tight": "calc(var(--tracking-normal) - 0.025em)", "tracking-wide": "calc(var(--tracking-normal) + 0.025em)", "tracking-wider": "calc(var(--tracking-normal) + 0.05em)", "tracking-widest": "calc(var(--tracking-normal) + 0.1em)"}, "light": {"background": "oklch(0.94 0.00 236.50)", "foreground": "oklch(0.32 0 0)", "card": "oklch(1.00 0 0)", "card-foreground": "oklch(0.32 0 0)", "popover": "oklch(1.00 0 0)", "popover-foreground": "oklch(0.32 0 0)", "primary": "oklch(0.64 0.17 36.44)", "primary-foreground": "oklch(1.00 0 0)", "secondary": "oklch(0.97 0.00 264.54)", "secondary-foreground": "oklch(0.45 0.03 256.80)", "muted": "oklch(0.98 0.00 247.84)", "muted-foreground": "oklch(0.55 0.02 264.36)", "accent": "oklch(0.91 0.02 243.82)", "accent-foreground": "oklch(0.38 0.14 265.52)", "destructive": "oklch(0.64 0.21 25.33)", "destructive-foreground": "oklch(1.00 0 0)", "border": "oklch(0.90 0.01 247.88)", "input": "oklch(0.97 0.00 264.54)", "ring": "oklch(0.64 0.17 36.44)", "chart-1": "oklch(0.72 0.06 248.68)", "chart-2": "oklch(0.79 0.09 35.96)", "chart-3": "oklch(0.58 0.08 254.16)", "chart-4": "oklch(0.50 0.08 259.49)", "chart-5": "oklch(0.42 0.10 264.03)", "radius": "0.75rem", "sidebar": "oklch(0.90 0.00 258.33)", "sidebar-foreground": "oklch(0.32 0 0)", "sidebar-primary": "oklch(0.64 0.17 36.44)", "sidebar-primary-foreground": "oklch(1.00 0 0)", "sidebar-accent": "oklch(0.91 0.02 243.82)", "sidebar-accent-foreground": "oklch(0.38 0.14 265.52)", "sidebar-border": "oklch(0.93 0.01 264.53)", "sidebar-ring": "oklch(0.64 0.17 36.44)", "font-sans": "Inter, sans-serif", "font-serif": "Source Serif 4, serif", "font-mono": "JetBrains Mono, monospace", "shadow-color": "hsl(0 0% 0%)", "shadow-opacity": "0.1", "shadow-blur": "3px", "shadow-spread": "0px", "shadow-offset-x": "0px", "shadow-offset-y": "1px", "letter-spacing": "0em", "spacing": "0.25rem", "shadow-2xs": "0px 1px 3px 0px hsl(0 0% 0% / 0.05)", "shadow-xs": "0px 1px 3px 0px hsl(0 0% 0% / 0.05)", "shadow-sm": "0px 1px 3px 0px hsl(0 0% 0% / 0.10), 0px 1px 2px -1px hsl(0 0% 0% / 0.10)", "shadow": "0px 1px 3px 0px hsl(0 0% 0% / 0.10), 0px 1px 2px -1px hsl(0 0% 0% / 0.10)", "shadow-md": "0px 1px 3px 0px hsl(0 0% 0% / 0.10), 0px 2px 4px -1px hsl(0 0% 0% / 0.10)", "shadow-lg": "0px 1px 3px 0px hsl(0 0% 0% / 0.10), 0px 4px 6px -1px hsl(0 0% 0% / 0.10)", "shadow-xl": "0px 1px 3px 0px hsl(0 0% 0% / 0.10), 0px 8px 10px -1px hsl(0 0% 0% / 0.10)", "shadow-2xl": "0px 1px 3px 0px hsl(0 0% 0% / 0.25)", "tracking-normal": "0em"}, "dark": {"background": "oklch(0.26 0.03 262.67)", "foreground": "oklch(0.92 0 0)", "card": "oklch(0.31 0.03 268.64)", "card-foreground": "oklch(0.92 0 0)", "popover": "oklch(0.29 0.02 268.40)", "popover-foreground": "oklch(0.92 0 0)", "primary": "oklch(0.64 0.17 36.44)", "primary-foreground": "oklch(1.00 0 0)", "secondary": "oklch(0.31 0.03 266.71)", "secondary-foreground": "oklch(0.92 0 0)", "muted": "oklch(0.31 0.03 266.71)", "muted-foreground": "oklch(0.72 0 0)", "accent": "oklch(0.34 0.06 267.59)", "accent-foreground": "oklch(0.88 0.06 254.13)", "destructive": "oklch(0.64 0.21 25.33)", "destructive-foreground": "oklch(1.00 0 0)", "border": "oklch(0.38 0.03 269.73)", "input": "oklch(0.38 0.03 269.73)", "ring": "oklch(0.64 0.17 36.44)", "chart-1": "oklch(0.72 0.06 248.68)", "chart-2": "oklch(0.77 0.09 34.19)", "chart-3": "oklch(0.58 0.08 254.16)", "chart-4": "oklch(0.50 0.08 259.49)", "chart-5": "oklch(0.42 0.10 264.03)", "radius": "0.75rem", "sidebar": "oklch(0.31 0.03 267.74)", "sidebar-foreground": "oklch(0.92 0 0)", "sidebar-primary": "oklch(0.64 0.17 36.44)", "sidebar-primary-foreground": "oklch(1.00 0 0)", "sidebar-accent": "oklch(0.34 0.06 267.59)", "sidebar-accent-foreground": "oklch(0.88 0.06 254.13)", "sidebar-border": "oklch(0.38 0.03 269.73)", "sidebar-ring": "oklch(0.64 0.17 36.44)", "font-sans": "Inter, sans-serif", "font-serif": "Source Serif 4, serif", "font-mono": "JetBrains Mono, monospace", "shadow-color": "hsl(0 0% 0%)", "shadow-opacity": "0.1", "shadow-blur": "3px", "shadow-spread": "0px", "shadow-offset-x": "0px", "shadow-offset-y": "1px", "letter-spacing": "0em", "spacing": "0.25rem", "shadow-2xs": "0px 1px 3px 0px hsl(0 0% 0% / 0.05)", "shadow-xs": "0px 1px 3px 0px hsl(0 0% 0% / 0.05)", "shadow-sm": "0px 1px 3px 0px hsl(0 0% 0% / 0.10), 0px 1px 2px -1px hsl(0 0% 0% / 0.10)", "shadow": "0px 1px 3px 0px hsl(0 0% 0% / 0.10), 0px 1px 2px -1px hsl(0 0% 0% / 0.10)", "shadow-md": "0px 1px 3px 0px hsl(0 0% 0% / 0.10), 0px 2px 4px -1px hsl(0 0% 0% / 0.10)", "shadow-lg": "0px 1px 3px 0px hsl(0 0% 0% / 0.10), 0px 4px 6px -1px hsl(0 0% 0% / 0.10)", "shadow-xl": "0px 1px 3px 0px hsl(0 0% 0% / 0.10), 0px 8px 10px -1px hsl(0 0% 0% / 0.10)", "shadow-2xl": "0px 1px 3px 0px hsl(0 0% 0% / 0.25)"}}}, {"name": "quantum-rose", "type": "registry:style", "title": "Quantum Rose", "description": "A theme based on the Quantum Rose color palette.", "css": {"@layer base": {"body": {"letter-spacing": "var(--tracking-normal)"}}}, "cssVars": {"theme": {"font-sans": "Quicksand, sans-serif", "font-mono": "Space Mono, monospace", "font-serif": "Playfair Display, serif", "radius": "0.5rem", "tracking-tighter": "calc(var(--tracking-normal) - 0.05em)", "tracking-tight": "calc(var(--tracking-normal) - 0.025em)", "tracking-wide": "calc(var(--tracking-normal) + 0.025em)", "tracking-wider": "calc(var(--tracking-normal) + 0.05em)", "tracking-widest": "calc(var(--tracking-normal) + 0.1em)"}, "light": {"background": "oklch(0.97 0.02 343.93)", "foreground": "oklch(0.44 0.17 352.38)", "card": "oklch(0.98 0.01 339.33)", "card-foreground": "oklch(0.44 0.17 352.38)", "popover": "oklch(0.98 0.01 339.33)", "popover-foreground": "oklch(0.44 0.17 352.38)", "primary": "oklch(0.60 0.24 0.13)", "primary-foreground": "oklch(1.00 0 0)", "secondary": "oklch(0.92 0.07 326.13)", "secondary-foreground": "oklch(0.44 0.17 352.38)", "muted": "oklch(0.94 0.04 344.26)", "muted-foreground": "oklch(0.57 0.17 352.05)", "accent": "oklch(0.88 0.08 344.88)", "accent-foreground": "oklch(0.44 0.17 352.38)", "destructive": "oklch(0.58 0.19 6.34)", "destructive-foreground": "oklch(1.00 0 0)", "border": "oklch(0.89 0.07 344.39)", "input": "oklch(0.92 0.07 326.13)", "ring": "oklch(0.60 0.24 0.13)", "chart-1": "oklch(0.60 0.24 0.13)", "chart-2": "oklch(0.60 0.17 345.04)", "chart-3": "oklch(0.60 0.12 311.80)", "chart-4": "oklch(0.58 0.12 283.29)", "chart-5": "oklch(0.65 0.19 267.97)", "radius": "0.5rem", "sidebar": "oklch(0.96 0.02 345.75)", "sidebar-foreground": "oklch(0.44 0.17 352.38)", "sidebar-primary": "oklch(0.60 0.24 0.13)", "sidebar-primary-foreground": "oklch(1.00 0 0)", "sidebar-accent": "oklch(0.88 0.08 344.88)", "sidebar-accent-foreground": "oklch(0.44 0.17 352.38)", "sidebar-border": "oklch(0.93 0.04 343.31)", "sidebar-ring": "oklch(0.60 0.24 0.13)", "font-sans": "<PERSON><PERSON><PERSON>, sans-serif", "font-serif": "Playfair Display, serif", "font-mono": "Space Mono, monospace", "shadow-color": "hsl(330 70% 30% / 0.12)", "shadow-opacity": "0.18", "shadow-blur": "0px", "shadow-spread": "0px", "shadow-offset-x": "0px", "shadow-offset-y": "3px", "letter-spacing": "0em", "spacing": "0.25rem", "shadow-2xs": "0px 3px 0px 0px hsl(330 70% 30% / 0.09)", "shadow-xs": "0px 3px 0px 0px hsl(330 70% 30% / 0.09)", "shadow-sm": "0px 3px 0px 0px hsl(330 70% 30% / 0.18), 0px 1px 2px -1px hsl(330 70% 30% / 0.18)", "shadow": "0px 3px 0px 0px hsl(330 70% 30% / 0.18), 0px 1px 2px -1px hsl(330 70% 30% / 0.18)", "shadow-md": "0px 3px 0px 0px hsl(330 70% 30% / 0.18), 0px 2px 4px -1px hsl(330 70% 30% / 0.18)", "shadow-lg": "0px 3px 0px 0px hsl(330 70% 30% / 0.18), 0px 4px 6px -1px hsl(330 70% 30% / 0.18)", "shadow-xl": "0px 3px 0px 0px hsl(330 70% 30% / 0.18), 0px 8px 10px -1px hsl(330 70% 30% / 0.18)", "shadow-2xl": "0px 3px 0px 0px hsl(330 70% 30% / 0.45)", "tracking-normal": "0em"}, "dark": {"background": "oklch(0.18 0.05 313.72)", "foreground": "oklch(0.86 0.13 326.64)", "card": "oklch(0.24 0.07 313.23)", "card-foreground": "oklch(0.86 0.13 326.64)", "popover": "oklch(0.24 0.07 313.23)", "popover-foreground": "oklch(0.86 0.13 326.64)", "primary": "oklch(0.75 0.23 332.02)", "primary-foreground": "oklch(0.16 0.05 327.57)", "secondary": "oklch(0.32 0.09 319.65)", "secondary-foreground": "oklch(0.86 0.13 326.64)", "muted": "oklch(0.27 0.08 312.35)", "muted-foreground": "oklch(0.71 0.16 327.11)", "accent": "oklch(0.36 0.12 325.77)", "accent-foreground": "oklch(0.86 0.13 326.64)", "destructive": "oklch(0.65 0.24 7.17)", "destructive-foreground": "oklch(0.98 0 0)", "border": "oklch(0.33 0.12 313.54)", "input": "oklch(0.32 0.09 319.65)", "ring": "oklch(0.75 0.23 332.02)", "chart-1": "oklch(0.75 0.23 332.02)", "chart-2": "oklch(0.65 0.22 317.63)", "chart-3": "oklch(0.62 0.22 292.77)", "chart-4": "oklch(0.61 0.16 278.72)", "chart-5": "oklch(0.62 0.20 268.05)", "radius": "0.5rem", "sidebar": "oklch(0.19 0.05 311.40)", "sidebar-foreground": "oklch(0.86 0.13 326.64)", "sidebar-primary": "oklch(0.75 0.23 332.02)", "sidebar-primary-foreground": "oklch(0.16 0.05 327.57)", "sidebar-accent": "oklch(0.36 0.12 325.77)", "sidebar-accent-foreground": "oklch(0.86 0.13 326.64)", "sidebar-border": "oklch(0.33 0.12 313.54)", "sidebar-ring": "oklch(0.75 0.23 332.02)", "font-sans": "Quicksand, sans-serif", "font-serif": "Playfair Display, serif", "font-mono": "Space Mono, monospace", "shadow-color": "hsl(300 80% 50% / 0.25)", "shadow-opacity": "0.18", "shadow-blur": "0px", "shadow-spread": "0px", "shadow-offset-x": "0px", "shadow-offset-y": "3px", "letter-spacing": "0em", "spacing": "0.25rem", "shadow-2xs": "0px 3px 0px 0px hsl(300 80% 50% / 0.09)", "shadow-xs": "0px 3px 0px 0px hsl(300 80% 50% / 0.09)", "shadow-sm": "0px 3px 0px 0px hsl(300 80% 50% / 0.18), 0px 1px 2px -1px hsl(300 80% 50% / 0.18)", "shadow": "0px 3px 0px 0px hsl(300 80% 50% / 0.18), 0px 1px 2px -1px hsl(300 80% 50% / 0.18)", "shadow-md": "0px 3px 0px 0px hsl(300 80% 50% / 0.18), 0px 2px 4px -1px hsl(300 80% 50% / 0.18)", "shadow-lg": "0px 3px 0px 0px hsl(300 80% 50% / 0.18), 0px 4px 6px -1px hsl(300 80% 50% / 0.18)", "shadow-xl": "0px 3px 0px 0px hsl(300 80% 50% / 0.18), 0px 8px 10px -1px hsl(300 80% 50% / 0.18)", "shadow-2xl": "0px 3px 0px 0px hsl(300 80% 50% / 0.45)"}}}, {"name": "nature", "type": "registry:style", "title": "Nature", "description": "A theme based on the Nature color palette.", "css": {"@layer base": {"body": {"letter-spacing": "var(--tracking-normal)"}}}, "cssVars": {"theme": {"font-sans": "Montserrat, sans-serif", "font-mono": "Source Code Pro, monospace", "font-serif": "<PERSON><PERSON><PERSON><PERSON>, serif", "radius": "0.5rem", "tracking-tighter": "calc(var(--tracking-normal) - 0.05em)", "tracking-tight": "calc(var(--tracking-normal) - 0.025em)", "tracking-wide": "calc(var(--tracking-normal) + 0.025em)", "tracking-wider": "calc(var(--tracking-normal) + 0.05em)", "tracking-widest": "calc(var(--tracking-normal) + 0.1em)"}, "light": {"background": "oklch(0.97 0.01 80.72)", "foreground": "oklch(0.30 0.04 30.20)", "card": "oklch(0.97 0.01 80.72)", "card-foreground": "oklch(0.30 0.04 30.20)", "popover": "oklch(0.97 0.01 80.72)", "popover-foreground": "oklch(0.30 0.04 30.20)", "primary": "oklch(0.52 0.13 144.17)", "primary-foreground": "oklch(1.00 0 0)", "secondary": "oklch(0.96 0.02 147.64)", "secondary-foreground": "oklch(0.43 0.12 144.31)", "muted": "oklch(0.94 0.01 74.42)", "muted-foreground": "oklch(0.45 0.05 39.21)", "accent": "oklch(0.90 0.05 146.04)", "accent-foreground": "oklch(0.43 0.12 144.31)", "destructive": "oklch(0.54 0.19 26.72)", "destructive-foreground": "oklch(1.00 0 0)", "border": "oklch(0.88 0.02 74.64)", "input": "oklch(0.88 0.02 74.64)", "ring": "oklch(0.52 0.13 144.17)", "chart-1": "oklch(0.67 0.16 144.21)", "chart-2": "oklch(0.58 0.14 144.18)", "chart-3": "oklch(0.52 0.13 144.17)", "chart-4": "oklch(0.43 0.12 144.31)", "chart-5": "oklch(0.22 0.05 145.73)", "radius": "0.5rem", "sidebar": "oklch(0.94 0.01 74.42)", "sidebar-foreground": "oklch(0.30 0.04 30.20)", "sidebar-primary": "oklch(0.52 0.13 144.17)", "sidebar-primary-foreground": "oklch(1.00 0 0)", "sidebar-accent": "oklch(0.90 0.05 146.04)", "sidebar-accent-foreground": "oklch(0.43 0.12 144.31)", "sidebar-border": "oklch(0.88 0.02 74.64)", "sidebar-ring": "oklch(0.52 0.13 144.17)", "font-sans": "Montserrat, sans-serif", "font-serif": "<PERSON><PERSON><PERSON><PERSON>, serif", "font-mono": "Source Code Pro, monospace", "shadow-color": "hsl(0 0% 0%)", "shadow-opacity": "0.1", "shadow-blur": "3px", "shadow-spread": "0px", "shadow-offset-x": "0", "shadow-offset-y": "1px", "letter-spacing": "0em", "spacing": "0.25rem", "shadow-2xs": "0 1px 3px 0px hsl(0 0% 0% / 0.05)", "shadow-xs": "0 1px 3px 0px hsl(0 0% 0% / 0.05)", "shadow-sm": "0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 1px 2px -1px hsl(0 0% 0% / 0.10)", "shadow": "0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 1px 2px -1px hsl(0 0% 0% / 0.10)", "shadow-md": "0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 2px 4px -1px hsl(0 0% 0% / 0.10)", "shadow-lg": "0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 4px 6px -1px hsl(0 0% 0% / 0.10)", "shadow-xl": "0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 8px 10px -1px hsl(0 0% 0% / 0.10)", "shadow-2xl": "0 1px 3px 0px hsl(0 0% 0% / 0.25)", "tracking-normal": "0em"}, "dark": {"background": "oklch(0.27 0.03 150.77)", "foreground": "oklch(0.94 0.01 72.66)", "card": "oklch(0.33 0.03 146.99)", "card-foreground": "oklch(0.94 0.01 72.66)", "popover": "oklch(0.33 0.03 146.99)", "popover-foreground": "oklch(0.94 0.01 72.66)", "primary": "oklch(0.67 0.16 144.21)", "primary-foreground": "oklch(0.22 0.05 145.73)", "secondary": "oklch(0.39 0.03 142.99)", "secondary-foreground": "oklch(0.90 0.02 142.55)", "muted": "oklch(0.33 0.03 146.99)", "muted-foreground": "oklch(0.86 0.02 76.10)", "accent": "oklch(0.58 0.14 144.18)", "accent-foreground": "oklch(0.94 0.01 72.66)", "destructive": "oklch(0.54 0.19 26.72)", "destructive-foreground": "oklch(0.94 0.01 72.66)", "border": "oklch(0.39 0.03 142.99)", "input": "oklch(0.39 0.03 142.99)", "ring": "oklch(0.67 0.16 144.21)", "chart-1": "oklch(0.77 0.12 145.30)", "chart-2": "oklch(0.72 0.14 144.89)", "chart-3": "oklch(0.67 0.16 144.21)", "chart-4": "oklch(0.63 0.15 144.20)", "chart-5": "oklch(0.58 0.14 144.18)", "radius": "0.5rem", "sidebar": "oklch(0.27 0.03 150.77)", "sidebar-foreground": "oklch(0.94 0.01 72.66)", "sidebar-primary": "oklch(0.67 0.16 144.21)", "sidebar-primary-foreground": "oklch(0.22 0.05 145.73)", "sidebar-accent": "oklch(0.58 0.14 144.18)", "sidebar-accent-foreground": "oklch(0.94 0.01 72.66)", "sidebar-border": "oklch(0.39 0.03 142.99)", "sidebar-ring": "oklch(0.67 0.16 144.21)", "font-sans": "Montserrat, sans-serif", "font-serif": "<PERSON><PERSON><PERSON><PERSON>, serif", "font-mono": "Source Code Pro, monospace", "shadow-color": "hsl(0 0% 0%)", "shadow-opacity": "0.1", "shadow-blur": "3px", "shadow-spread": "0px", "shadow-offset-x": "0", "shadow-offset-y": "1px", "letter-spacing": "0em", "spacing": "0.25rem", "shadow-2xs": "0 1px 3px 0px hsl(0 0% 0% / 0.05)", "shadow-xs": "0 1px 3px 0px hsl(0 0% 0% / 0.05)", "shadow-sm": "0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 1px 2px -1px hsl(0 0% 0% / 0.10)", "shadow": "0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 1px 2px -1px hsl(0 0% 0% / 0.10)", "shadow-md": "0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 2px 4px -1px hsl(0 0% 0% / 0.10)", "shadow-lg": "0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 4px 6px -1px hsl(0 0% 0% / 0.10)", "shadow-xl": "0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 8px 10px -1px hsl(0 0% 0% / 0.10)", "shadow-2xl": "0 1px 3px 0px hsl(0 0% 0% / 0.25)"}}}, {"name": "bold-tech", "type": "registry:style", "title": "Bold Tech", "description": "A theme based on the Bold Tech color palette.", "css": {"@layer base": {"body": {"letter-spacing": "var(--tracking-normal)"}}}, "cssVars": {"theme": {"font-sans": "Roboto, sans-serif", "font-mono": "Fira Code, monospace", "font-serif": "Playfair Display, serif", "radius": "0.625rem", "tracking-tighter": "calc(var(--tracking-normal) - 0.05em)", "tracking-tight": "calc(var(--tracking-normal) - 0.025em)", "tracking-wide": "calc(var(--tracking-normal) + 0.025em)", "tracking-wider": "calc(var(--tracking-normal) + 0.05em)", "tracking-widest": "calc(var(--tracking-normal) + 0.1em)"}, "light": {"background": "oklch(1.00 0 0)", "foreground": "oklch(0.36 0.14 278.70)", "card": "oklch(1.00 0 0)", "card-foreground": "oklch(0.36 0.14 278.70)", "popover": "oklch(1.00 0 0)", "popover-foreground": "oklch(0.36 0.14 278.70)", "primary": "oklch(0.61 0.22 292.72)", "primary-foreground": "oklch(1.00 0 0)", "secondary": "oklch(0.96 0.02 295.19)", "secondary-foreground": "oklch(0.46 0.21 277.02)", "muted": "oklch(0.97 0.02 293.76)", "muted-foreground": "oklch(0.54 0.25 293.01)", "accent": "oklch(0.93 0.03 255.59)", "accent-foreground": "oklch(0.42 0.18 265.64)", "destructive": "oklch(0.64 0.21 25.33)", "destructive-foreground": "oklch(1.00 0 0)", "border": "oklch(0.93 0.03 272.79)", "input": "oklch(0.93 0.03 272.79)", "ring": "oklch(0.61 0.22 292.72)", "chart-1": "oklch(0.61 0.22 292.72)", "chart-2": "oklch(0.54 0.25 293.01)", "chart-3": "oklch(0.49 0.24 292.58)", "chart-4": "oklch(0.43 0.21 292.76)", "chart-5": "oklch(0.38 0.18 293.74)", "radius": "0.625rem", "sidebar": "oklch(0.97 0.02 293.76)", "sidebar-foreground": "oklch(0.36 0.14 278.70)", "sidebar-primary": "oklch(0.61 0.22 292.72)", "sidebar-primary-foreground": "oklch(1.00 0 0)", "sidebar-accent": "oklch(0.93 0.03 255.59)", "sidebar-accent-foreground": "oklch(0.42 0.18 265.64)", "sidebar-border": "oklch(0.93 0.03 272.79)", "sidebar-ring": "oklch(0.61 0.22 292.72)", "font-sans": "Roboto, sans-serif", "font-serif": "Playfair Display, serif", "font-mono": "Fira Code, monospace", "shadow-color": "hsl(255 86% 66%)", "shadow-opacity": "0.2", "shadow-blur": "4px", "shadow-spread": "0px", "shadow-offset-x": "2px", "shadow-offset-y": "2px", "letter-spacing": "0em", "spacing": "0.25rem", "shadow-2xs": "2px 2px 4px 0px hsl(255 86% 66% / 0.10)", "shadow-xs": "2px 2px 4px 0px hsl(255 86% 66% / 0.10)", "shadow-sm": "2px 2px 4px 0px hsl(255 86% 66% / 0.20), 2px 1px 2px -1px hsl(255 86% 66% / 0.20)", "shadow": "2px 2px 4px 0px hsl(255 86% 66% / 0.20), 2px 1px 2px -1px hsl(255 86% 66% / 0.20)", "shadow-md": "2px 2px 4px 0px hsl(255 86% 66% / 0.20), 2px 2px 4px -1px hsl(255 86% 66% / 0.20)", "shadow-lg": "2px 2px 4px 0px hsl(255 86% 66% / 0.20), 2px 4px 6px -1px hsl(255 86% 66% / 0.20)", "shadow-xl": "2px 2px 4px 0px hsl(255 86% 66% / 0.20), 2px 8px 10px -1px hsl(255 86% 66% / 0.20)", "shadow-2xl": "2px 2px 4px 0px hsl(255 86% 66% / 0.50)", "tracking-normal": "0em"}, "dark": {"background": "oklch(0.21 0.04 265.75)", "foreground": "oklch(0.93 0.03 272.79)", "card": "oklch(0.26 0.09 281.29)", "card-foreground": "oklch(0.93 0.03 272.79)", "popover": "oklch(0.26 0.09 281.29)", "popover-foreground": "oklch(0.93 0.03 272.79)", "primary": "oklch(0.61 0.22 292.72)", "primary-foreground": "oklch(1.00 0 0)", "secondary": "oklch(0.26 0.09 281.29)", "secondary-foreground": "oklch(0.93 0.03 272.79)", "muted": "oklch(0.26 0.09 281.29)", "muted-foreground": "oklch(0.81 0.10 293.57)", "accent": "oklch(0.46 0.21 277.02)", "accent-foreground": "oklch(0.93 0.03 272.79)", "destructive": "oklch(0.64 0.21 25.33)", "destructive-foreground": "oklch(1.00 0 0)", "border": "oklch(0.28 0.14 291.09)", "input": "oklch(0.28 0.14 291.09)", "ring": "oklch(0.61 0.22 292.72)", "chart-1": "oklch(0.71 0.16 293.54)", "chart-2": "oklch(0.61 0.22 292.72)", "chart-3": "oklch(0.54 0.25 293.01)", "chart-4": "oklch(0.49 0.24 292.58)", "chart-5": "oklch(0.43 0.21 292.76)", "radius": "0.625rem", "sidebar": "oklch(0.21 0.04 265.75)", "sidebar-foreground": "oklch(0.93 0.03 272.79)", "sidebar-primary": "oklch(0.61 0.22 292.72)", "sidebar-primary-foreground": "oklch(1.00 0 0)", "sidebar-accent": "oklch(0.46 0.21 277.02)", "sidebar-accent-foreground": "oklch(0.93 0.03 272.79)", "sidebar-border": "oklch(0.28 0.14 291.09)", "sidebar-ring": "oklch(0.61 0.22 292.72)", "font-sans": "Roboto, sans-serif", "font-serif": "Playfair Display, serif", "font-mono": "Fira Code, monospace", "shadow-color": "hsl(255 86% 66%)", "shadow-opacity": "0.2", "shadow-blur": "4px", "shadow-spread": "0px", "shadow-offset-x": "2px", "shadow-offset-y": "2px", "letter-spacing": "0em", "spacing": "0.25rem", "shadow-2xs": "2px 2px 4px 0px hsl(255 86% 66% / 0.10)", "shadow-xs": "2px 2px 4px 0px hsl(255 86% 66% / 0.10)", "shadow-sm": "2px 2px 4px 0px hsl(255 86% 66% / 0.20), 2px 1px 2px -1px hsl(255 86% 66% / 0.20)", "shadow": "2px 2px 4px 0px hsl(255 86% 66% / 0.20), 2px 1px 2px -1px hsl(255 86% 66% / 0.20)", "shadow-md": "2px 2px 4px 0px hsl(255 86% 66% / 0.20), 2px 2px 4px -1px hsl(255 86% 66% / 0.20)", "shadow-lg": "2px 2px 4px 0px hsl(255 86% 66% / 0.20), 2px 4px 6px -1px hsl(255 86% 66% / 0.20)", "shadow-xl": "2px 2px 4px 0px hsl(255 86% 66% / 0.20), 2px 8px 10px -1px hsl(255 86% 66% / 0.20)", "shadow-2xl": "2px 2px 4px 0px hsl(255 86% 66% / 0.50)"}}}, {"name": "elegant-luxury", "type": "registry:style", "title": "<PERSON><PERSON><PERSON> Lu<PERSON>", "description": "A theme based on the Elegant Luxury color palette.", "css": {"@layer base": {"body": {"letter-spacing": "var(--tracking-normal)"}}}, "cssVars": {"theme": {"font-sans": "<PERSON><PERSON><PERSON>, sans-serif", "font-mono": "IBM Plex Mono, monospace", "font-serif": "Libre Baskerville, serif", "radius": "0.375rem", "tracking-tighter": "calc(var(--tracking-normal) - 0.05em)", "tracking-tight": "calc(var(--tracking-normal) - 0.025em)", "tracking-wide": "calc(var(--tracking-normal) + 0.025em)", "tracking-wider": "calc(var(--tracking-normal) + 0.05em)", "tracking-widest": "calc(var(--tracking-normal) + 0.1em)"}, "light": {"background": "oklch(0.98 0.00 56.38)", "foreground": "oklch(0.22 0 0)", "card": "oklch(0.98 0.00 56.38)", "card-foreground": "oklch(0.22 0 0)", "popover": "oklch(0.98 0.00 56.38)", "popover-foreground": "oklch(0.22 0 0)", "primary": "oklch(0.47 0.15 24.94)", "primary-foreground": "oklch(1.00 0 0)", "secondary": "oklch(0.96 0.04 89.09)", "secondary-foreground": "oklch(0.48 0.10 75.12)", "muted": "oklch(0.94 0.01 53.44)", "muted-foreground": "oklch(0.44 0.01 73.64)", "accent": "oklch(0.96 0.06 95.62)", "accent-foreground": "oklch(0.40 0.13 25.72)", "destructive": "oklch(0.44 0.16 26.90)", "destructive-foreground": "oklch(1.00 0 0)", "border": "oklch(0.94 0.03 80.99)", "input": "oklch(0.94 0.03 80.99)", "ring": "oklch(0.47 0.15 24.94)", "chart-1": "oklch(0.51 0.19 27.52)", "chart-2": "oklch(0.47 0.15 24.94)", "chart-3": "oklch(0.40 0.13 25.72)", "chart-4": "oklch(0.56 0.15 49.00)", "chart-5": "oklch(0.47 0.12 46.20)", "radius": "0.375rem", "sidebar": "oklch(0.94 0.01 53.44)", "sidebar-foreground": "oklch(0.22 0 0)", "sidebar-primary": "oklch(0.47 0.15 24.94)", "sidebar-primary-foreground": "oklch(1.00 0 0)", "sidebar-accent": "oklch(0.96 0.06 95.62)", "sidebar-accent-foreground": "oklch(0.40 0.13 25.72)", "sidebar-border": "oklch(0.94 0.03 80.99)", "sidebar-ring": "oklch(0.47 0.15 24.94)", "font-sans": "<PERSON><PERSON><PERSON>, sans-serif", "font-serif": "Libre Baskerville, serif", "font-mono": "IBM Plex Mono, monospace", "shadow-color": "hsl(0 63% 18%)", "shadow-opacity": "0.12", "shadow-blur": "16px", "shadow-spread": "-2px", "shadow-offset-x": "1px", "shadow-offset-y": "1px", "letter-spacing": "0em", "spacing": "0.25rem", "shadow-2xs": "1px 1px 16px -2px hsl(0 63% 18% / 0.06)", "shadow-xs": "1px 1px 16px -2px hsl(0 63% 18% / 0.06)", "shadow-sm": "1px 1px 16px -2px hsl(0 63% 18% / 0.12), 1px 1px 2px -3px hsl(0 63% 18% / 0.12)", "shadow": "1px 1px 16px -2px hsl(0 63% 18% / 0.12), 1px 1px 2px -3px hsl(0 63% 18% / 0.12)", "shadow-md": "1px 1px 16px -2px hsl(0 63% 18% / 0.12), 1px 2px 4px -3px hsl(0 63% 18% / 0.12)", "shadow-lg": "1px 1px 16px -2px hsl(0 63% 18% / 0.12), 1px 4px 6px -3px hsl(0 63% 18% / 0.12)", "shadow-xl": "1px 1px 16px -2px hsl(0 63% 18% / 0.12), 1px 8px 10px -3px hsl(0 63% 18% / 0.12)", "shadow-2xl": "1px 1px 16px -2px hsl(0 63% 18% / 0.30)", "tracking-normal": "0em"}, "dark": {"background": "oklch(0.22 0.01 56.04)", "foreground": "oklch(0.97 0.00 106.42)", "card": "oklch(0.27 0.01 34.30)", "card-foreground": "oklch(0.97 0.00 106.42)", "popover": "oklch(0.27 0.01 34.30)", "popover-foreground": "oklch(0.97 0.00 106.42)", "primary": "oklch(0.51 0.19 27.52)", "primary-foreground": "oklch(0.98 0.00 56.38)", "secondary": "oklch(0.47 0.12 46.20)", "secondary-foreground": "oklch(0.96 0.06 95.62)", "muted": "oklch(0.27 0.01 34.30)", "muted-foreground": "oklch(0.87 0.00 56.37)", "accent": "oklch(0.56 0.15 49.00)", "accent-foreground": "oklch(0.96 0.06 95.62)", "destructive": "oklch(0.64 0.21 25.33)", "destructive-foreground": "oklch(1.00 0 0)", "border": "oklch(0.37 0.01 67.56)", "input": "oklch(0.37 0.01 67.56)", "ring": "oklch(0.51 0.19 27.52)", "chart-1": "oklch(0.71 0.17 22.22)", "chart-2": "oklch(0.64 0.21 25.33)", "chart-3": "oklch(0.58 0.22 27.33)", "chart-4": "oklch(0.84 0.16 84.43)", "chart-5": "oklch(0.77 0.16 70.08)", "radius": "0.375rem", "sidebar": "oklch(0.22 0.01 56.04)", "sidebar-foreground": "oklch(0.97 0.00 106.42)", "sidebar-primary": "oklch(0.51 0.19 27.52)", "sidebar-primary-foreground": "oklch(0.98 0.00 56.38)", "sidebar-accent": "oklch(0.56 0.15 49.00)", "sidebar-accent-foreground": "oklch(0.96 0.06 95.62)", "sidebar-border": "oklch(0.37 0.01 67.56)", "sidebar-ring": "oklch(0.51 0.19 27.52)", "font-sans": "<PERSON><PERSON><PERSON>, sans-serif", "font-serif": "Libre Baskerville, serif", "font-mono": "IBM Plex Mono, monospace", "shadow-color": "hsl(0 63% 18%)", "shadow-opacity": "0.12", "shadow-blur": "16px", "shadow-spread": "-2px", "shadow-offset-x": "1px", "shadow-offset-y": "1px", "letter-spacing": "0em", "spacing": "0.25rem", "shadow-2xs": "1px 1px 16px -2px hsl(0 63% 18% / 0.06)", "shadow-xs": "1px 1px 16px -2px hsl(0 63% 18% / 0.06)", "shadow-sm": "1px 1px 16px -2px hsl(0 63% 18% / 0.12), 1px 1px 2px -3px hsl(0 63% 18% / 0.12)", "shadow": "1px 1px 16px -2px hsl(0 63% 18% / 0.12), 1px 1px 2px -3px hsl(0 63% 18% / 0.12)", "shadow-md": "1px 1px 16px -2px hsl(0 63% 18% / 0.12), 1px 2px 4px -3px hsl(0 63% 18% / 0.12)", "shadow-lg": "1px 1px 16px -2px hsl(0 63% 18% / 0.12), 1px 4px 6px -3px hsl(0 63% 18% / 0.12)", "shadow-xl": "1px 1px 16px -2px hsl(0 63% 18% / 0.12), 1px 8px 10px -3px hsl(0 63% 18% / 0.12)", "shadow-2xl": "1px 1px 16px -2px hsl(0 63% 18% / 0.30)"}}}, {"name": "amber-minimal", "type": "registry:style", "title": "<PERSON>", "description": "A theme based on the Amber Minimal color palette.", "css": {"@layer base": {"body": {"letter-spacing": "var(--tracking-normal)"}}}, "cssVars": {"theme": {"font-sans": "Inter, sans-serif", "font-mono": "JetBrains Mono, monospace", "font-serif": "Source Serif 4, serif", "radius": "0.375rem", "tracking-tighter": "calc(var(--tracking-normal) - 0.05em)", "tracking-tight": "calc(var(--tracking-normal) - 0.025em)", "tracking-wide": "calc(var(--tracking-normal) + 0.025em)", "tracking-wider": "calc(var(--tracking-normal) + 0.05em)", "tracking-widest": "calc(var(--tracking-normal) + 0.1em)"}, "light": {"background": "oklch(1.00 0 0)", "foreground": "oklch(0.27 0 0)", "card": "oklch(1.00 0 0)", "card-foreground": "oklch(0.27 0 0)", "popover": "oklch(1.00 0 0)", "popover-foreground": "oklch(0.27 0 0)", "primary": "oklch(0.77 0.16 70.08)", "primary-foreground": "oklch(0 0 0)", "secondary": "oklch(0.97 0.00 264.54)", "secondary-foreground": "oklch(0.45 0.03 256.80)", "muted": "oklch(0.98 0.00 247.84)", "muted-foreground": "oklch(0.55 0.02 264.36)", "accent": "oklch(0.99 0.02 95.28)", "accent-foreground": "oklch(0.47 0.12 46.20)", "destructive": "oklch(0.64 0.21 25.33)", "destructive-foreground": "oklch(1.00 0 0)", "border": "oklch(0.93 0.01 264.53)", "input": "oklch(0.93 0.01 264.53)", "ring": "oklch(0.77 0.16 70.08)", "chart-1": "oklch(0.77 0.16 70.08)", "chart-2": "oklch(0.67 0.16 58.32)", "chart-3": "oklch(0.56 0.15 49.00)", "chart-4": "oklch(0.47 0.12 46.20)", "chart-5": "oklch(0.41 0.11 45.90)", "radius": "0.375rem", "sidebar": "oklch(0.98 0.00 247.84)", "sidebar-foreground": "oklch(0.27 0 0)", "sidebar-primary": "oklch(0.77 0.16 70.08)", "sidebar-primary-foreground": "oklch(1.00 0 0)", "sidebar-accent": "oklch(0.99 0.02 95.28)", "sidebar-accent-foreground": "oklch(0.47 0.12 46.20)", "sidebar-border": "oklch(0.93 0.01 264.53)", "sidebar-ring": "oklch(0.77 0.16 70.08)", "font-sans": "Inter, sans-serif", "font-serif": "Source Serif 4, serif", "font-mono": "JetBrains Mono, monospace", "shadow-color": "hsl(0 0% 0%)", "shadow-opacity": "0.1", "shadow-blur": "8px", "shadow-spread": "-1px", "shadow-offset-x": "0px", "shadow-offset-y": "4px", "letter-spacing": "0em", "spacing": "0.25rem", "shadow-2xs": "0px 4px 8px -1px hsl(0 0% 0% / 0.05)", "shadow-xs": "0px 4px 8px -1px hsl(0 0% 0% / 0.05)", "shadow-sm": "0px 4px 8px -1px hsl(0 0% 0% / 0.10), 0px 1px 2px -2px hsl(0 0% 0% / 0.10)", "shadow": "0px 4px 8px -1px hsl(0 0% 0% / 0.10), 0px 1px 2px -2px hsl(0 0% 0% / 0.10)", "shadow-md": "0px 4px 8px -1px hsl(0 0% 0% / 0.10), 0px 2px 4px -2px hsl(0 0% 0% / 0.10)", "shadow-lg": "0px 4px 8px -1px hsl(0 0% 0% / 0.10), 0px 4px 6px -2px hsl(0 0% 0% / 0.10)", "shadow-xl": "0px 4px 8px -1px hsl(0 0% 0% / 0.10), 0px 8px 10px -2px hsl(0 0% 0% / 0.10)", "shadow-2xl": "0px 4px 8px -1px hsl(0 0% 0% / 0.25)", "tracking-normal": "0em"}, "dark": {"background": "oklch(0.20 0 0)", "foreground": "oklch(0.92 0 0)", "card": "oklch(0.27 0 0)", "card-foreground": "oklch(0.92 0 0)", "popover": "oklch(0.27 0 0)", "popover-foreground": "oklch(0.92 0 0)", "primary": "oklch(0.77 0.16 70.08)", "primary-foreground": "oklch(0 0 0)", "secondary": "oklch(0.27 0 0)", "secondary-foreground": "oklch(0.92 0 0)", "muted": "oklch(0.27 0 0)", "muted-foreground": "oklch(0.72 0 0)", "accent": "oklch(0.47 0.12 46.20)", "accent-foreground": "oklch(0.92 0.12 95.75)", "destructive": "oklch(0.64 0.21 25.33)", "destructive-foreground": "oklch(1.00 0 0)", "border": "oklch(0.37 0 0)", "input": "oklch(0.37 0 0)", "ring": "oklch(0.77 0.16 70.08)", "chart-1": "oklch(0.84 0.16 84.43)", "chart-2": "oklch(0.67 0.16 58.32)", "chart-3": "oklch(0.47 0.12 46.20)", "chart-4": "oklch(0.56 0.15 49.00)", "chart-5": "oklch(0.47 0.12 46.20)", "radius": "0.375rem", "sidebar": "oklch(0.17 0 0)", "sidebar-foreground": "oklch(0.92 0 0)", "sidebar-primary": "oklch(0.77 0.16 70.08)", "sidebar-primary-foreground": "oklch(1.00 0 0)", "sidebar-accent": "oklch(0.47 0.12 46.20)", "sidebar-accent-foreground": "oklch(0.92 0.12 95.75)", "sidebar-border": "oklch(0.37 0 0)", "sidebar-ring": "oklch(0.77 0.16 70.08)", "font-sans": "Inter, sans-serif", "font-serif": "Source Serif 4, serif", "font-mono": "JetBrains Mono, monospace", "shadow-color": "hsl(0 0% 0%)", "shadow-opacity": "0.1", "shadow-blur": "8px", "shadow-spread": "-1px", "shadow-offset-x": "0px", "shadow-offset-y": "4px", "letter-spacing": "0em", "spacing": "0.25rem", "shadow-2xs": "0px 4px 8px -1px hsl(0 0% 0% / 0.05)", "shadow-xs": "0px 4px 8px -1px hsl(0 0% 0% / 0.05)", "shadow-sm": "0px 4px 8px -1px hsl(0 0% 0% / 0.10), 0px 1px 2px -2px hsl(0 0% 0% / 0.10)", "shadow": "0px 4px 8px -1px hsl(0 0% 0% / 0.10), 0px 1px 2px -2px hsl(0 0% 0% / 0.10)", "shadow-md": "0px 4px 8px -1px hsl(0 0% 0% / 0.10), 0px 2px 4px -2px hsl(0 0% 0% / 0.10)", "shadow-lg": "0px 4px 8px -1px hsl(0 0% 0% / 0.10), 0px 4px 6px -2px hsl(0 0% 0% / 0.10)", "shadow-xl": "0px 4px 8px -1px hsl(0 0% 0% / 0.10), 0px 8px 10px -2px hsl(0 0% 0% / 0.10)", "shadow-2xl": "0px 4px 8px -1px hsl(0 0% 0% / 0.25)"}}}, {"name": "supabase", "type": "registry:style", "title": "Supabase", "description": "A theme based on the Supabase color palette.", "css": {"@layer base": {"body": {"letter-spacing": "var(--tracking-normal)"}}}, "cssVars": {"theme": {"font-sans": "Outfit, sans-serif", "font-mono": "monospace", "font-serif": "ui-serif, Georgia, Cambria, \"Times New Roman\", Times, serif", "radius": "0.5rem", "tracking-tighter": "calc(var(--tracking-normal) - 0.05em)", "tracking-tight": "calc(var(--tracking-normal) - 0.025em)", "tracking-wide": "calc(var(--tracking-normal) + 0.025em)", "tracking-wider": "calc(var(--tracking-normal) + 0.05em)", "tracking-widest": "calc(var(--tracking-normal) + 0.1em)"}, "light": {"background": "oklch(0.99 0 0)", "foreground": "oklch(0.20 0 0)", "card": "oklch(0.99 0 0)", "card-foreground": "oklch(0.20 0 0)", "popover": "oklch(0.99 0 0)", "popover-foreground": "oklch(0.44 0 0)", "primary": "oklch(0.83 0.13 160.91)", "primary-foreground": "oklch(0.26 0.01 166.46)", "secondary": "oklch(0.99 0 0)", "secondary-foreground": "oklch(0.20 0 0)", "muted": "oklch(0.95 0 0)", "muted-foreground": "oklch(0.24 0 0)", "accent": "oklch(0.95 0 0)", "accent-foreground": "oklch(0.24 0 0)", "destructive": "oklch(0.55 0.19 32.73)", "destructive-foreground": "oklch(0.99 0.00 17.21)", "border": "oklch(0.90 0 0)", "input": "oklch(0.97 0 0)", "ring": "oklch(0.83 0.13 160.91)", "chart-1": "oklch(0.83 0.13 160.91)", "chart-2": "oklch(0.62 0.19 259.81)", "chart-3": "oklch(0.61 0.22 292.72)", "chart-4": "oklch(0.77 0.16 70.08)", "chart-5": "oklch(0.70 0.15 162.48)", "radius": "0.5rem", "sidebar": "oklch(0.99 0 0)", "sidebar-foreground": "oklch(0.55 0 0)", "sidebar-primary": "oklch(0.83 0.13 160.91)", "sidebar-primary-foreground": "oklch(0.26 0.01 166.46)", "sidebar-accent": "oklch(0.95 0 0)", "sidebar-accent-foreground": "oklch(0.24 0 0)", "sidebar-border": "oklch(0.90 0 0)", "sidebar-ring": "oklch(0.83 0.13 160.91)", "font-sans": "Outfit, sans-serif", "font-serif": "ui-serif, Georgia, Cambria, \"Times New Roman\", Times, serif", "font-mono": "monospace", "shadow-color": "#000000", "shadow-opacity": "0.17", "shadow-blur": "3px", "shadow-spread": "0px", "shadow-offset-x": "0px", "shadow-offset-y": "1px", "letter-spacing": "0.025em", "spacing": "0.25rem", "shadow-2xs": "0px 1px 3px 0px hsl(0 0% 0% / 0.09)", "shadow-xs": "0px 1px 3px 0px hsl(0 0% 0% / 0.09)", "shadow-sm": "0px 1px 3px 0px hsl(0 0% 0% / 0.17), 0px 1px 2px -1px hsl(0 0% 0% / 0.17)", "shadow": "0px 1px 3px 0px hsl(0 0% 0% / 0.17), 0px 1px 2px -1px hsl(0 0% 0% / 0.17)", "shadow-md": "0px 1px 3px 0px hsl(0 0% 0% / 0.17), 0px 2px 4px -1px hsl(0 0% 0% / 0.17)", "shadow-lg": "0px 1px 3px 0px hsl(0 0% 0% / 0.17), 0px 4px 6px -1px hsl(0 0% 0% / 0.17)", "shadow-xl": "0px 1px 3px 0px hsl(0 0% 0% / 0.17), 0px 8px 10px -1px hsl(0 0% 0% / 0.17)", "shadow-2xl": "0px 1px 3px 0px hsl(0 0% 0% / 0.43)", "tracking-normal": "0.025em"}, "dark": {"background": "oklch(0.18 0 0)", "foreground": "oklch(0.93 0.01 255.51)", "card": "oklch(0.20 0 0)", "card-foreground": "oklch(0.93 0.01 255.51)", "popover": "oklch(0.26 0 0)", "popover-foreground": "oklch(0.73 0 0)", "primary": "oklch(0.44 0.10 156.76)", "primary-foreground": "oklch(0.92 0.01 167.16)", "secondary": "oklch(0.26 0 0)", "secondary-foreground": "oklch(0.99 0 0)", "muted": "oklch(0.24 0 0)", "muted-foreground": "oklch(0.71 0 0)", "accent": "oklch(0.31 0 0)", "accent-foreground": "oklch(0.99 0 0)", "destructive": "oklch(0.31 0.09 29.79)", "destructive-foreground": "oklch(0.94 0.00 34.31)", "border": "oklch(0.28 0 0)", "input": "oklch(0.26 0 0)", "ring": "oklch(0.80 0.18 151.71)", "chart-1": "oklch(0.80 0.18 151.71)", "chart-2": "oklch(0.71 0.14 254.62)", "chart-3": "oklch(0.71 0.16 293.54)", "chart-4": "oklch(0.84 0.16 84.43)", "chart-5": "oklch(0.78 0.13 181.91)", "radius": "0.5rem", "sidebar": "oklch(0.18 0 0)", "sidebar-foreground": "oklch(0.63 0 0)", "sidebar-primary": "oklch(0.44 0.10 156.76)", "sidebar-primary-foreground": "oklch(0.92 0.01 167.16)", "sidebar-accent": "oklch(0.31 0 0)", "sidebar-accent-foreground": "oklch(0.99 0 0)", "sidebar-border": "oklch(0.28 0 0)", "sidebar-ring": "oklch(0.80 0.18 151.71)", "font-sans": "Outfit, sans-serif", "font-serif": "ui-serif, Georgia, Cambria, \"Times New Roman\", Times, serif", "font-mono": "monospace", "shadow-color": "#000000", "shadow-opacity": "0.17", "shadow-blur": "3px", "shadow-spread": "0px", "shadow-offset-x": "0px", "shadow-offset-y": "1px", "letter-spacing": "0.025em", "spacing": "0.25rem", "shadow-2xs": "0px 1px 3px 0px hsl(0 0% 0% / 0.09)", "shadow-xs": "0px 1px 3px 0px hsl(0 0% 0% / 0.09)", "shadow-sm": "0px 1px 3px 0px hsl(0 0% 0% / 0.17), 0px 1px 2px -1px hsl(0 0% 0% / 0.17)", "shadow": "0px 1px 3px 0px hsl(0 0% 0% / 0.17), 0px 1px 2px -1px hsl(0 0% 0% / 0.17)", "shadow-md": "0px 1px 3px 0px hsl(0 0% 0% / 0.17), 0px 2px 4px -1px hsl(0 0% 0% / 0.17)", "shadow-lg": "0px 1px 3px 0px hsl(0 0% 0% / 0.17), 0px 4px 6px -1px hsl(0 0% 0% / 0.17)", "shadow-xl": "0px 1px 3px 0px hsl(0 0% 0% / 0.17), 0px 8px 10px -1px hsl(0 0% 0% / 0.17)", "shadow-2xl": "0px 1px 3px 0px hsl(0 0% 0% / 0.43)"}}}, {"name": "neo-brutalism", "type": "registry:style", "title": "Neo Brutalism", "description": "A theme based on the Neo Brutalism color palette.", "css": {"@layer base": {"body": {"letter-spacing": "var(--tracking-normal)"}}}, "cssVars": {"theme": {"font-sans": "DM Sans, sans-serif", "font-mono": "Space Mono, monospace", "font-serif": "ui-serif, Georgia, Cambria, \"Times New Roman\", Times, serif", "radius": "0px", "tracking-tighter": "calc(var(--tracking-normal) - 0.05em)", "tracking-tight": "calc(var(--tracking-normal) - 0.025em)", "tracking-wide": "calc(var(--tracking-normal) + 0.025em)", "tracking-wider": "calc(var(--tracking-normal) + 0.05em)", "tracking-widest": "calc(var(--tracking-normal) + 0.1em)"}, "light": {"background": "oklch(1.00 0 0)", "foreground": "oklch(0 0 0)", "card": "oklch(1.00 0 0)", "card-foreground": "oklch(0 0 0)", "popover": "oklch(1.00 0 0)", "popover-foreground": "oklch(0 0 0)", "primary": "oklch(0.65 0.24 26.97)", "primary-foreground": "oklch(1.00 0 0)", "secondary": "oklch(0.97 0.21 109.77)", "secondary-foreground": "oklch(0 0 0)", "muted": "oklch(0.96 0 0)", "muted-foreground": "oklch(0.32 0 0)", "accent": "oklch(0.56 0.24 260.82)", "accent-foreground": "oklch(1.00 0 0)", "destructive": "oklch(0 0 0)", "destructive-foreground": "oklch(1.00 0 0)", "border": "oklch(0 0 0)", "input": "oklch(0 0 0)", "ring": "oklch(0.65 0.24 26.97)", "chart-1": "oklch(0.65 0.24 26.97)", "chart-2": "oklch(0.97 0.21 109.77)", "chart-3": "oklch(0.56 0.24 260.82)", "chart-4": "oklch(0.73 0.25 142.50)", "chart-5": "oklch(0.59 0.27 328.36)", "radius": "0px", "sidebar": "oklch(0.96 0 0)", "sidebar-foreground": "oklch(0 0 0)", "sidebar-primary": "oklch(0.65 0.24 26.97)", "sidebar-primary-foreground": "oklch(1.00 0 0)", "sidebar-accent": "oklch(0.56 0.24 260.82)", "sidebar-accent-foreground": "oklch(1.00 0 0)", "sidebar-border": "oklch(0 0 0)", "sidebar-ring": "oklch(0.65 0.24 26.97)", "font-sans": "DM Sans, sans-serif", "font-serif": "ui-serif, Georgia, Cambria, \"Times New Roman\", Times, serif", "font-mono": "Space Mono, monospace", "shadow-color": "hsl(0 0% 0%)", "shadow-opacity": "1", "shadow-blur": "0px", "shadow-spread": "0px", "shadow-offset-x": "4px", "shadow-offset-y": "4px", "letter-spacing": "0em", "spacing": "0.25rem", "shadow-2xs": "4px 4px 0px 0px hsl(0 0% 0% / 0.50)", "shadow-xs": "4px 4px 0px 0px hsl(0 0% 0% / 0.50)", "shadow-sm": "4px 4px 0px 0px hsl(0 0% 0% / 1.00), 4px 1px 2px -1px hsl(0 0% 0% / 1.00)", "shadow": "4px 4px 0px 0px hsl(0 0% 0% / 1.00), 4px 1px 2px -1px hsl(0 0% 0% / 1.00)", "shadow-md": "4px 4px 0px 0px hsl(0 0% 0% / 1.00), 4px 2px 4px -1px hsl(0 0% 0% / 1.00)", "shadow-lg": "4px 4px 0px 0px hsl(0 0% 0% / 1.00), 4px 4px 6px -1px hsl(0 0% 0% / 1.00)", "shadow-xl": "4px 4px 0px 0px hsl(0 0% 0% / 1.00), 4px 8px 10px -1px hsl(0 0% 0% / 1.00)", "shadow-2xl": "4px 4px 0px 0px hsl(0 0% 0% / 2.50)", "tracking-normal": "0em"}, "dark": {"background": "oklch(0 0 0)", "foreground": "oklch(1.00 0 0)", "card": "oklch(0.32 0 0)", "card-foreground": "oklch(1.00 0 0)", "popover": "oklch(0.32 0 0)", "popover-foreground": "oklch(1.00 0 0)", "primary": "oklch(0.70 0.19 23.19)", "primary-foreground": "oklch(0 0 0)", "secondary": "oklch(0.97 0.20 109.62)", "secondary-foreground": "oklch(0 0 0)", "muted": "oklch(0.32 0 0)", "muted-foreground": "oklch(0.85 0 0)", "accent": "oklch(0.68 0.18 252.26)", "accent-foreground": "oklch(0 0 0)", "destructive": "oklch(1.00 0 0)", "destructive-foreground": "oklch(0 0 0)", "border": "oklch(1.00 0 0)", "input": "oklch(1.00 0 0)", "ring": "oklch(0.70 0.19 23.19)", "chart-1": "oklch(0.70 0.19 23.19)", "chart-2": "oklch(0.97 0.20 109.62)", "chart-3": "oklch(0.68 0.18 252.26)", "chart-4": "oklch(0.74 0.23 142.85)", "chart-5": "oklch(0.61 0.25 328.07)", "radius": "0px", "sidebar": "oklch(0 0 0)", "sidebar-foreground": "oklch(1.00 0 0)", "sidebar-primary": "oklch(0.70 0.19 23.19)", "sidebar-primary-foreground": "oklch(0 0 0)", "sidebar-accent": "oklch(0.68 0.18 252.26)", "sidebar-accent-foreground": "oklch(0 0 0)", "sidebar-border": "oklch(1.00 0 0)", "sidebar-ring": "oklch(0.70 0.19 23.19)", "font-sans": "DM Sans, sans-serif", "font-serif": "ui-serif, Georgia, Cambria, \"Times New Roman\", Times, serif", "font-mono": "Space Mono, monospace", "shadow-color": "hsl(0 0% 0%)", "shadow-opacity": "1", "shadow-blur": "0px", "shadow-spread": "0px", "shadow-offset-x": "4px", "shadow-offset-y": "4px", "letter-spacing": "0em", "spacing": "0.25rem", "shadow-2xs": "4px 4px 0px 0px hsl(0 0% 0% / 0.50)", "shadow-xs": "4px 4px 0px 0px hsl(0 0% 0% / 0.50)", "shadow-sm": "4px 4px 0px 0px hsl(0 0% 0% / 1.00), 4px 1px 2px -1px hsl(0 0% 0% / 1.00)", "shadow": "4px 4px 0px 0px hsl(0 0% 0% / 1.00), 4px 1px 2px -1px hsl(0 0% 0% / 1.00)", "shadow-md": "4px 4px 0px 0px hsl(0 0% 0% / 1.00), 4px 2px 4px -1px hsl(0 0% 0% / 1.00)", "shadow-lg": "4px 4px 0px 0px hsl(0 0% 0% / 1.00), 4px 4px 6px -1px hsl(0 0% 0% / 1.00)", "shadow-xl": "4px 4px 0px 0px hsl(0 0% 0% / 1.00), 4px 8px 10px -1px hsl(0 0% 0% / 1.00)", "shadow-2xl": "4px 4px 0px 0px hsl(0 0% 0% / 2.50)"}}}, {"name": "solar-dusk", "type": "registry:style", "title": "Solar Dusk", "description": "A theme based on the Solar Dusk color palette.", "css": {"@layer base": {"body": {"letter-spacing": "var(--tracking-normal)"}}}, "cssVars": {"theme": {"font-sans": "Oxanium, sans-serif", "font-mono": "Fira Code, monospace", "font-serif": "<PERSON><PERSON><PERSON><PERSON>, serif", "radius": "0.3rem", "tracking-tighter": "calc(var(--tracking-normal) - 0.05em)", "tracking-tight": "calc(var(--tracking-normal) - 0.025em)", "tracking-wide": "calc(var(--tracking-normal) + 0.025em)", "tracking-wider": "calc(var(--tracking-normal) + 0.05em)", "tracking-widest": "calc(var(--tracking-normal) + 0.1em)"}, "light": {"background": "oklch(0.99 0.01 84.57)", "foreground": "oklch(0.37 0.03 49.61)", "card": "oklch(0.97 0.01 78.28)", "card-foreground": "oklch(0.37 0.03 49.61)", "popover": "oklch(0.97 0.01 78.28)", "popover-foreground": "oklch(0.37 0.03 49.61)", "primary": "oklch(0.56 0.15 49.00)", "primary-foreground": "oklch(1.00 0 0)", "secondary": "oklch(0.83 0.08 74.44)", "secondary-foreground": "oklch(0.44 0.01 73.64)", "muted": "oklch(0.94 0.02 83.26)", "muted-foreground": "oklch(0.55 0.01 58.07)", "accent": "oklch(0.90 0.05 74.99)", "accent-foreground": "oklch(0.44 0.01 73.64)", "destructive": "oklch(0.44 0.16 26.90)", "destructive-foreground": "oklch(1.00 0 0)", "border": "oklch(0.89 0.04 89.70)", "input": "oklch(0.89 0.04 89.70)", "ring": "oklch(0.56 0.15 49.00)", "chart-1": "oklch(0.56 0.15 49.00)", "chart-2": "oklch(0.55 0.01 58.07)", "chart-3": "oklch(0.55 0.12 66.44)", "chart-4": "oklch(0.55 0.01 58.07)", "chart-5": "oklch(0.68 0.14 75.83)", "radius": "0.3rem", "sidebar": "oklch(0.94 0.02 83.26)", "sidebar-foreground": "oklch(0.37 0.03 49.61)", "sidebar-primary": "oklch(0.56 0.15 49.00)", "sidebar-primary-foreground": "oklch(1.00 0 0)", "sidebar-accent": "oklch(0.55 0.12 66.44)", "sidebar-accent-foreground": "oklch(1.00 0 0)", "sidebar-border": "oklch(0.89 0.04 89.70)", "sidebar-ring": "oklch(0.56 0.15 49.00)", "font-sans": "Oxanium, sans-serif", "font-serif": "<PERSON><PERSON><PERSON><PERSON>, serif", "font-mono": "Fira Code, monospace", "shadow-color": "hsl(28 18% 25%)", "shadow-opacity": "0.18", "shadow-blur": "3px", "shadow-spread": "0px", "shadow-offset-x": "0px", "shadow-offset-y": "2px", "letter-spacing": "0em", "spacing": "0.25rem", "shadow-2xs": "0px 2px 3px 0px hsl(28 18% 25% / 0.09)", "shadow-xs": "0px 2px 3px 0px hsl(28 18% 25% / 0.09)", "shadow-sm": "0px 2px 3px 0px hsl(28 18% 25% / 0.18), 0px 1px 2px -1px hsl(28 18% 25% / 0.18)", "shadow": "0px 2px 3px 0px hsl(28 18% 25% / 0.18), 0px 1px 2px -1px hsl(28 18% 25% / 0.18)", "shadow-md": "0px 2px 3px 0px hsl(28 18% 25% / 0.18), 0px 2px 4px -1px hsl(28 18% 25% / 0.18)", "shadow-lg": "0px 2px 3px 0px hsl(28 18% 25% / 0.18), 0px 4px 6px -1px hsl(28 18% 25% / 0.18)", "shadow-xl": "0px 2px 3px 0px hsl(28 18% 25% / 0.18), 0px 8px 10px -1px hsl(28 18% 25% / 0.18)", "shadow-2xl": "0px 2px 3px 0px hsl(28 18% 25% / 0.45)", "tracking-normal": "0em"}, "dark": {"background": "oklch(0.22 0.01 56.04)", "foreground": "oklch(0.97 0.00 106.42)", "card": "oklch(0.27 0.01 34.30)", "card-foreground": "oklch(0.97 0.00 106.42)", "popover": "oklch(0.27 0.01 34.30)", "popover-foreground": "oklch(0.97 0.00 106.42)", "primary": "oklch(0.70 0.19 47.60)", "primary-foreground": "oklch(1.00 0 0)", "secondary": "oklch(0.44 0.01 73.64)", "secondary-foreground": "oklch(0.92 0.00 48.72)", "muted": "oklch(0.27 0.01 34.30)", "muted-foreground": "oklch(0.72 0.01 56.26)", "accent": "oklch(0.36 0.05 229.32)", "accent-foreground": "oklch(0.92 0.00 48.72)", "destructive": "oklch(0.58 0.22 27.33)", "destructive-foreground": "oklch(1.00 0 0)", "border": "oklch(0.37 0.01 67.56)", "input": "oklch(0.37 0.01 67.56)", "ring": "oklch(0.70 0.19 47.60)", "chart-1": "oklch(0.70 0.19 47.60)", "chart-2": "oklch(0.68 0.15 237.32)", "chart-3": "oklch(0.80 0.16 86.05)", "chart-4": "oklch(0.72 0.01 56.26)", "chart-5": "oklch(0.55 0.01 58.07)", "radius": "0.3rem", "sidebar": "oklch(0.27 0.01 34.30)", "sidebar-foreground": "oklch(0.97 0.00 106.42)", "sidebar-primary": "oklch(0.70 0.19 47.60)", "sidebar-primary-foreground": "oklch(1.00 0 0)", "sidebar-accent": "oklch(0.68 0.15 237.32)", "sidebar-accent-foreground": "oklch(0.28 0.07 254.54)", "sidebar-border": "oklch(0.37 0.01 67.56)", "sidebar-ring": "oklch(0.70 0.19 47.60)", "font-sans": "Oxanium, sans-serif", "font-serif": "<PERSON><PERSON><PERSON><PERSON>, serif", "font-mono": "Fira Code, monospace", "shadow-color": "hsl(0 0% 5%)", "shadow-opacity": "0.18", "shadow-blur": "3px", "shadow-spread": "0px", "shadow-offset-x": "0px", "shadow-offset-y": "2px", "letter-spacing": "0em", "spacing": "0.25rem", "shadow-2xs": "0px 2px 3px 0px hsl(0 0% 5% / 0.09)", "shadow-xs": "0px 2px 3px 0px hsl(0 0% 5% / 0.09)", "shadow-sm": "0px 2px 3px 0px hsl(0 0% 5% / 0.18), 0px 1px 2px -1px hsl(0 0% 5% / 0.18)", "shadow": "0px 2px 3px 0px hsl(0 0% 5% / 0.18), 0px 1px 2px -1px hsl(0 0% 5% / 0.18)", "shadow-md": "0px 2px 3px 0px hsl(0 0% 5% / 0.18), 0px 2px 4px -1px hsl(0 0% 5% / 0.18)", "shadow-lg": "0px 2px 3px 0px hsl(0 0% 5% / 0.18), 0px 4px 6px -1px hsl(0 0% 5% / 0.18)", "shadow-xl": "0px 2px 3px 0px hsl(0 0% 5% / 0.18), 0px 8px 10px -1px hsl(0 0% 5% / 0.18)", "shadow-2xl": "0px 2px 3px 0px hsl(0 0% 5% / 0.45)"}}}, {"name": "claymorphism", "type": "registry:style", "title": "Claymorphism", "description": "A theme based on the Claymorphism color palette.", "css": {"@layer base": {"body": {"letter-spacing": "var(--tracking-normal)"}}}, "cssVars": {"theme": {"font-sans": "Plus Jakarta Sans, sans-serif", "font-mono": "Roboto Mono, monospace", "font-serif": "Lo<PERSON>, serif", "radius": "1.25rem", "tracking-tighter": "calc(var(--tracking-normal) - 0.05em)", "tracking-tight": "calc(var(--tracking-normal) - 0.025em)", "tracking-wide": "calc(var(--tracking-normal) + 0.025em)", "tracking-wider": "calc(var(--tracking-normal) + 0.05em)", "tracking-widest": "calc(var(--tracking-normal) + 0.1em)"}, "light": {"background": "oklch(0.92 0.00 48.72)", "foreground": "oklch(0.28 0.04 260.03)", "card": "oklch(0.97 0.00 106.42)", "card-foreground": "oklch(0.28 0.04 260.03)", "popover": "oklch(0.97 0.00 106.42)", "popover-foreground": "oklch(0.28 0.04 260.03)", "primary": "oklch(0.59 0.20 277.12)", "primary-foreground": "oklch(1.00 0 0)", "secondary": "oklch(0.87 0.00 56.37)", "secondary-foreground": "oklch(0.45 0.03 256.80)", "muted": "oklch(0.92 0.00 48.72)", "muted-foreground": "oklch(0.55 0.02 264.36)", "accent": "oklch(0.94 0.03 321.94)", "accent-foreground": "oklch(0.37 0.03 259.73)", "destructive": "oklch(0.64 0.21 25.33)", "destructive-foreground": "oklch(1.00 0 0)", "border": "oklch(0.87 0.00 56.37)", "input": "oklch(0.87 0.00 56.37)", "ring": "oklch(0.59 0.20 277.12)", "chart-1": "oklch(0.59 0.20 277.12)", "chart-2": "oklch(0.51 0.23 276.97)", "chart-3": "oklch(0.46 0.21 277.02)", "chart-4": "oklch(0.40 0.18 277.37)", "chart-5": "oklch(0.36 0.14 278.70)", "radius": "1.25rem", "sidebar": "oklch(0.87 0.00 56.37)", "sidebar-foreground": "oklch(0.28 0.04 260.03)", "sidebar-primary": "oklch(0.59 0.20 277.12)", "sidebar-primary-foreground": "oklch(1.00 0 0)", "sidebar-accent": "oklch(0.94 0.03 321.94)", "sidebar-accent-foreground": "oklch(0.37 0.03 259.73)", "sidebar-border": "oklch(0.87 0.00 56.37)", "sidebar-ring": "oklch(0.59 0.20 277.12)", "font-sans": "Plus Jakarta Sans, sans-serif", "font-serif": "Lo<PERSON>, serif", "font-mono": "Roboto Mono, monospace", "shadow-color": "hsl(240 4% 60%)", "shadow-opacity": "0.18", "shadow-blur": "10px", "shadow-spread": "4px", "shadow-offset-x": "2px", "shadow-offset-y": "2px", "letter-spacing": "0em", "spacing": "0.25rem", "shadow-2xs": "2px 2px 10px 4px hsl(240 4% 60% / 0.09)", "shadow-xs": "2px 2px 10px 4px hsl(240 4% 60% / 0.09)", "shadow-sm": "2px 2px 10px 4px hsl(240 4% 60% / 0.18), 2px 1px 2px 3px hsl(240 4% 60% / 0.18)", "shadow": "2px 2px 10px 4px hsl(240 4% 60% / 0.18), 2px 1px 2px 3px hsl(240 4% 60% / 0.18)", "shadow-md": "2px 2px 10px 4px hsl(240 4% 60% / 0.18), 2px 2px 4px 3px hsl(240 4% 60% / 0.18)", "shadow-lg": "2px 2px 10px 4px hsl(240 4% 60% / 0.18), 2px 4px 6px 3px hsl(240 4% 60% / 0.18)", "shadow-xl": "2px 2px 10px 4px hsl(240 4% 60% / 0.18), 2px 8px 10px 3px hsl(240 4% 60% / 0.18)", "shadow-2xl": "2px 2px 10px 4px hsl(240 4% 60% / 0.45)", "tracking-normal": "0em"}, "dark": {"background": "oklch(0.22 0.01 67.44)", "foreground": "oklch(0.93 0.01 255.51)", "card": "oklch(0.28 0.01 59.34)", "card-foreground": "oklch(0.93 0.01 255.51)", "popover": "oklch(0.28 0.01 59.34)", "popover-foreground": "oklch(0.93 0.01 255.51)", "primary": "oklch(0.68 0.16 276.93)", "primary-foreground": "oklch(0.22 0.01 67.44)", "secondary": "oklch(0.34 0.01 59.42)", "secondary-foreground": "oklch(0.87 0.01 258.34)", "muted": "oklch(0.28 0.01 59.34)", "muted-foreground": "oklch(0.71 0.02 261.32)", "accent": "oklch(0.39 0.01 59.47)", "accent-foreground": "oklch(0.87 0.01 258.34)", "destructive": "oklch(0.64 0.21 25.33)", "destructive-foreground": "oklch(0.22 0.01 67.44)", "border": "oklch(0.34 0.01 59.42)", "input": "oklch(0.34 0.01 59.42)", "ring": "oklch(0.68 0.16 276.93)", "chart-1": "oklch(0.68 0.16 276.93)", "chart-2": "oklch(0.59 0.20 277.12)", "chart-3": "oklch(0.51 0.23 276.97)", "chart-4": "oklch(0.46 0.21 277.02)", "chart-5": "oklch(0.40 0.18 277.37)", "radius": "1.25rem", "sidebar": "oklch(0.34 0.01 59.42)", "sidebar-foreground": "oklch(0.93 0.01 255.51)", "sidebar-primary": "oklch(0.68 0.16 276.93)", "sidebar-primary-foreground": "oklch(0.22 0.01 67.44)", "sidebar-accent": "oklch(0.39 0.01 59.47)", "sidebar-accent-foreground": "oklch(0.87 0.01 258.34)", "sidebar-border": "oklch(0.34 0.01 59.42)", "sidebar-ring": "oklch(0.68 0.16 276.93)", "font-sans": "Plus Jakarta Sans, sans-serif", "font-serif": "Lo<PERSON>, serif", "font-mono": "Roboto Mono, monospace", "shadow-color": "hsl(0 0% 0%)", "shadow-opacity": "0.18", "shadow-blur": "10px", "shadow-spread": "4px", "shadow-offset-x": "2px", "shadow-offset-y": "2px", "letter-spacing": "0em", "spacing": "0.25rem", "shadow-2xs": "2px 2px 10px 4px hsl(0 0% 0% / 0.09)", "shadow-xs": "2px 2px 10px 4px hsl(0 0% 0% / 0.09)", "shadow-sm": "2px 2px 10px 4px hsl(0 0% 0% / 0.18), 2px 1px 2px 3px hsl(0 0% 0% / 0.18)", "shadow": "2px 2px 10px 4px hsl(0 0% 0% / 0.18), 2px 1px 2px 3px hsl(0 0% 0% / 0.18)", "shadow-md": "2px 2px 10px 4px hsl(0 0% 0% / 0.18), 2px 2px 4px 3px hsl(0 0% 0% / 0.18)", "shadow-lg": "2px 2px 10px 4px hsl(0 0% 0% / 0.18), 2px 4px 6px 3px hsl(0 0% 0% / 0.18)", "shadow-xl": "2px 2px 10px 4px hsl(0 0% 0% / 0.18), 2px 8px 10px 3px hsl(0 0% 0% / 0.18)", "shadow-2xl": "2px 2px 10px 4px hsl(0 0% 0% / 0.45)"}}}, {"name": "cyberpunk", "type": "registry:style", "title": "Cyberpunk", "description": "A theme based on the Cyberpunk color palette.", "css": {"@layer base": {"body": {"letter-spacing": "var(--tracking-normal)"}}}, "cssVars": {"theme": {"font-sans": "Outfit, sans-serif", "font-mono": "Fira Code, monospace", "font-serif": "ui-serif, Georgia, Cambria, \"Times New Roman\", Times, serif", "radius": "0.5rem", "tracking-tighter": "calc(var(--tracking-normal) - 0.05em)", "tracking-tight": "calc(var(--tracking-normal) - 0.025em)", "tracking-wide": "calc(var(--tracking-normal) + 0.025em)", "tracking-wider": "calc(var(--tracking-normal) + 0.05em)", "tracking-widest": "calc(var(--tracking-normal) + 0.1em)"}, "light": {"background": "oklch(0.98 0.00 247.84)", "foreground": "oklch(0.16 0.04 281.83)", "card": "oklch(1.00 0 0)", "card-foreground": "oklch(0.16 0.04 281.83)", "popover": "oklch(1.00 0 0)", "popover-foreground": "oklch(0.16 0.04 281.83)", "primary": "oklch(0.67 0.29 341.41)", "primary-foreground": "oklch(1.00 0 0)", "secondary": "oklch(0.96 0.02 286.02)", "secondary-foreground": "oklch(0.16 0.04 281.83)", "muted": "oklch(0.96 0.02 286.02)", "muted-foreground": "oklch(0.16 0.04 281.83)", "accent": "oklch(0.89 0.17 171.27)", "accent-foreground": "oklch(0.16 0.04 281.83)", "destructive": "oklch(0.65 0.23 34.04)", "destructive-foreground": "oklch(1.00 0 0)", "border": "oklch(0.92 0.01 225.09)", "input": "oklch(0.92 0.01 225.09)", "ring": "oklch(0.67 0.29 341.41)", "chart-1": "oklch(0.67 0.29 341.41)", "chart-2": "oklch(0.55 0.29 299.10)", "chart-3": "oklch(0.84 0.15 209.29)", "chart-4": "oklch(0.89 0.17 171.27)", "chart-5": "oklch(0.92 0.19 101.41)", "radius": "0.5rem", "sidebar": "oklch(0.96 0.02 286.02)", "sidebar-foreground": "oklch(0.16 0.04 281.83)", "sidebar-primary": "oklch(0.67 0.29 341.41)", "sidebar-primary-foreground": "oklch(1.00 0 0)", "sidebar-accent": "oklch(0.89 0.17 171.27)", "sidebar-accent-foreground": "oklch(0.16 0.04 281.83)", "sidebar-border": "oklch(0.92 0.01 225.09)", "sidebar-ring": "oklch(0.67 0.29 341.41)", "font-sans": "Outfit, sans-serif", "font-serif": "ui-serif, Georgia, Cambria, \"Times New Roman\", Times, serif", "font-mono": "Fira Code, monospace", "shadow-color": "hsl(0 0% 0%)", "shadow-opacity": "0.1", "shadow-blur": "8px", "shadow-spread": "-2px", "shadow-offset-x": "0px", "shadow-offset-y": "4px", "letter-spacing": "0em", "spacing": "0.25rem", "shadow-2xs": "0px 4px 8px -2px hsl(0 0% 0% / 0.05)", "shadow-xs": "0px 4px 8px -2px hsl(0 0% 0% / 0.05)", "shadow-sm": "0px 4px 8px -2px hsl(0 0% 0% / 0.10), 0px 1px 2px -3px hsl(0 0% 0% / 0.10)", "shadow": "0px 4px 8px -2px hsl(0 0% 0% / 0.10), 0px 1px 2px -3px hsl(0 0% 0% / 0.10)", "shadow-md": "0px 4px 8px -2px hsl(0 0% 0% / 0.10), 0px 2px 4px -3px hsl(0 0% 0% / 0.10)", "shadow-lg": "0px 4px 8px -2px hsl(0 0% 0% / 0.10), 0px 4px 6px -3px hsl(0 0% 0% / 0.10)", "shadow-xl": "0px 4px 8px -2px hsl(0 0% 0% / 0.10), 0px 8px 10px -3px hsl(0 0% 0% / 0.10)", "shadow-2xl": "0px 4px 8px -2px hsl(0 0% 0% / 0.25)", "tracking-normal": "0em"}, "dark": {"background": "oklch(0.16 0.04 281.83)", "foreground": "oklch(0.95 0.01 260.73)", "card": "oklch(0.25 0.06 281.14)", "card-foreground": "oklch(0.95 0.01 260.73)", "popover": "oklch(0.25 0.06 281.14)", "popover-foreground": "oklch(0.95 0.01 260.73)", "primary": "oklch(0.67 0.29 341.41)", "primary-foreground": "oklch(1.00 0 0)", "secondary": "oklch(0.25 0.06 281.14)", "secondary-foreground": "oklch(0.95 0.01 260.73)", "muted": "oklch(0.25 0.06 281.14)", "muted-foreground": "oklch(0.62 0.05 278.10)", "accent": "oklch(0.89 0.17 171.27)", "accent-foreground": "oklch(0.16 0.04 281.83)", "destructive": "oklch(0.65 0.23 34.04)", "destructive-foreground": "oklch(1.00 0 0)", "border": "oklch(0.33 0.08 280.79)", "input": "oklch(0.33 0.08 280.79)", "ring": "oklch(0.67 0.29 341.41)", "chart-1": "oklch(0.67 0.29 341.41)", "chart-2": "oklch(0.55 0.29 299.10)", "chart-3": "oklch(0.84 0.15 209.29)", "chart-4": "oklch(0.89 0.17 171.27)", "chart-5": "oklch(0.92 0.19 101.41)", "radius": "0.5rem", "sidebar": "oklch(0.16 0.04 281.83)", "sidebar-foreground": "oklch(0.95 0.01 260.73)", "sidebar-primary": "oklch(0.67 0.29 341.41)", "sidebar-primary-foreground": "oklch(1.00 0 0)", "sidebar-accent": "oklch(0.89 0.17 171.27)", "sidebar-accent-foreground": "oklch(0.16 0.04 281.83)", "sidebar-border": "oklch(0.33 0.08 280.79)", "sidebar-ring": "oklch(0.67 0.29 341.41)", "font-sans": "Outfit, sans-serif", "font-serif": "ui-serif, Georgia, Cambria, \"Times New Roman\", Times, serif", "font-mono": "Fira Code, monospace", "shadow-color": "hsl(0 0% 0%)", "shadow-opacity": "0.1", "shadow-blur": "8px", "shadow-spread": "-2px", "shadow-offset-x": "0px", "shadow-offset-y": "4px", "letter-spacing": "0em", "spacing": "0.25rem", "shadow-2xs": "0px 4px 8px -2px hsl(0 0% 0% / 0.05)", "shadow-xs": "0px 4px 8px -2px hsl(0 0% 0% / 0.05)", "shadow-sm": "0px 4px 8px -2px hsl(0 0% 0% / 0.10), 0px 1px 2px -3px hsl(0 0% 0% / 0.10)", "shadow": "0px 4px 8px -2px hsl(0 0% 0% / 0.10), 0px 1px 2px -3px hsl(0 0% 0% / 0.10)", "shadow-md": "0px 4px 8px -2px hsl(0 0% 0% / 0.10), 0px 2px 4px -3px hsl(0 0% 0% / 0.10)", "shadow-lg": "0px 4px 8px -2px hsl(0 0% 0% / 0.10), 0px 4px 6px -3px hsl(0 0% 0% / 0.10)", "shadow-xl": "0px 4px 8px -2px hsl(0 0% 0% / 0.10), 0px 8px 10px -3px hsl(0 0% 0% / 0.10)", "shadow-2xl": "0px 4px 8px -2px hsl(0 0% 0% / 0.25)"}}}, {"name": "pastel-dreams", "type": "registry:style", "title": "Pastel Dreams", "description": "A theme based on the Pastel Dreams color palette.", "css": {"@layer base": {"body": {"letter-spacing": "var(--tracking-normal)"}}}, "cssVars": {"theme": {"font-sans": "Open Sans, sans-serif", "font-mono": "IBM Plex Mono, monospace", "font-serif": "Source Serif 4, serif", "radius": "1.5rem", "tracking-tighter": "calc(var(--tracking-normal) - 0.05em)", "tracking-tight": "calc(var(--tracking-normal) - 0.025em)", "tracking-wide": "calc(var(--tracking-normal) + 0.025em)", "tracking-wider": "calc(var(--tracking-normal) + 0.05em)", "tracking-widest": "calc(var(--tracking-normal) + 0.1em)"}, "light": {"background": "oklch(0.97 0.01 314.78)", "foreground": "oklch(0.37 0.03 259.73)", "card": "oklch(1.00 0 0)", "card-foreground": "oklch(0.37 0.03 259.73)", "popover": "oklch(1.00 0 0)", "popover-foreground": "oklch(0.37 0.03 259.73)", "primary": "oklch(0.71 0.16 293.54)", "primary-foreground": "oklch(1.00 0 0)", "secondary": "oklch(0.91 0.05 306.09)", "secondary-foreground": "oklch(0.45 0.03 256.80)", "muted": "oklch(0.95 0.03 307.17)", "muted-foreground": "oklch(0.55 0.02 264.36)", "accent": "oklch(0.94 0.03 321.94)", "accent-foreground": "oklch(0.37 0.03 259.73)", "destructive": "oklch(0.81 0.10 19.57)", "destructive-foreground": "oklch(1.00 0 0)", "border": "oklch(0.91 0.05 306.09)", "input": "oklch(0.91 0.05 306.09)", "ring": "oklch(0.71 0.16 293.54)", "chart-1": "oklch(0.71 0.16 293.54)", "chart-2": "oklch(0.61 0.22 292.72)", "chart-3": "oklch(0.54 0.25 293.01)", "chart-4": "oklch(0.49 0.24 292.58)", "chart-5": "oklch(0.43 0.21 292.76)", "radius": "1.5rem", "sidebar": "oklch(0.91 0.05 306.09)", "sidebar-foreground": "oklch(0.37 0.03 259.73)", "sidebar-primary": "oklch(0.71 0.16 293.54)", "sidebar-primary-foreground": "oklch(1.00 0 0)", "sidebar-accent": "oklch(0.94 0.03 321.94)", "sidebar-accent-foreground": "oklch(0.37 0.03 259.73)", "sidebar-border": "oklch(0.91 0.05 306.09)", "sidebar-ring": "oklch(0.71 0.16 293.54)", "font-sans": "Open Sans, sans-serif", "font-serif": "Source Serif 4, serif", "font-mono": "IBM Plex Mono, monospace", "shadow-color": "hsl(0 0% 0%)", "shadow-opacity": "0.08", "shadow-blur": "16px", "shadow-spread": "-4px", "shadow-offset-x": "0px", "shadow-offset-y": "8px", "letter-spacing": "0em", "spacing": "0.25rem", "shadow-2xs": "0px 8px 16px -4px hsl(0 0% 0% / 0.04)", "shadow-xs": "0px 8px 16px -4px hsl(0 0% 0% / 0.04)", "shadow-sm": "0px 8px 16px -4px hsl(0 0% 0% / 0.08), 0px 1px 2px -5px hsl(0 0% 0% / 0.08)", "shadow": "0px 8px 16px -4px hsl(0 0% 0% / 0.08), 0px 1px 2px -5px hsl(0 0% 0% / 0.08)", "shadow-md": "0px 8px 16px -4px hsl(0 0% 0% / 0.08), 0px 2px 4px -5px hsl(0 0% 0% / 0.08)", "shadow-lg": "0px 8px 16px -4px hsl(0 0% 0% / 0.08), 0px 4px 6px -5px hsl(0 0% 0% / 0.08)", "shadow-xl": "0px 8px 16px -4px hsl(0 0% 0% / 0.08), 0px 8px 10px -5px hsl(0 0% 0% / 0.08)", "shadow-2xl": "0px 8px 16px -4px hsl(0 0% 0% / 0.20)", "tracking-normal": "0em"}, "dark": {"background": "oklch(0.22 0.01 56.04)", "foreground": "oklch(0.93 0.03 272.79)", "card": "oklch(0.28 0.03 307.23)", "card-foreground": "oklch(0.93 0.03 272.79)", "popover": "oklch(0.28 0.03 307.23)", "popover-foreground": "oklch(0.93 0.03 272.79)", "primary": "oklch(0.79 0.12 295.75)", "primary-foreground": "oklch(0.22 0.01 56.04)", "secondary": "oklch(0.34 0.04 308.85)", "secondary-foreground": "oklch(0.87 0.01 258.34)", "muted": "oklch(0.28 0.03 307.23)", "muted-foreground": "oklch(0.71 0.02 261.32)", "accent": "oklch(0.39 0.05 304.64)", "accent-foreground": "oklch(0.87 0.01 258.34)", "destructive": "oklch(0.81 0.10 19.57)", "destructive-foreground": "oklch(0.22 0.01 56.04)", "border": "oklch(0.34 0.04 308.85)", "input": "oklch(0.34 0.04 308.85)", "ring": "oklch(0.79 0.12 295.75)", "chart-1": "oklch(0.79 0.12 295.75)", "chart-2": "oklch(0.71 0.16 293.54)", "chart-3": "oklch(0.61 0.22 292.72)", "chart-4": "oklch(0.54 0.25 293.01)", "chart-5": "oklch(0.49 0.24 292.58)", "radius": "1.5rem", "sidebar": "oklch(0.34 0.04 308.85)", "sidebar-foreground": "oklch(0.93 0.03 272.79)", "sidebar-primary": "oklch(0.79 0.12 295.75)", "sidebar-primary-foreground": "oklch(0.22 0.01 56.04)", "sidebar-accent": "oklch(0.39 0.05 304.64)", "sidebar-accent-foreground": "oklch(0.87 0.01 258.34)", "sidebar-border": "oklch(0.34 0.04 308.85)", "sidebar-ring": "oklch(0.79 0.12 295.75)", "font-sans": "Open Sans, sans-serif", "font-serif": "Source Serif 4, serif", "font-mono": "IBM Plex Mono, monospace", "shadow-color": "hsl(0 0% 0%)", "shadow-opacity": "0.08", "shadow-blur": "16px", "shadow-spread": "-4px", "shadow-offset-x": "0px", "shadow-offset-y": "8px", "letter-spacing": "0em", "spacing": "0.25rem", "shadow-2xs": "0px 8px 16px -4px hsl(0 0% 0% / 0.04)", "shadow-xs": "0px 8px 16px -4px hsl(0 0% 0% / 0.04)", "shadow-sm": "0px 8px 16px -4px hsl(0 0% 0% / 0.08), 0px 1px 2px -5px hsl(0 0% 0% / 0.08)", "shadow": "0px 8px 16px -4px hsl(0 0% 0% / 0.08), 0px 1px 2px -5px hsl(0 0% 0% / 0.08)", "shadow-md": "0px 8px 16px -4px hsl(0 0% 0% / 0.08), 0px 2px 4px -5px hsl(0 0% 0% / 0.08)", "shadow-lg": "0px 8px 16px -4px hsl(0 0% 0% / 0.08), 0px 4px 6px -5px hsl(0 0% 0% / 0.08)", "shadow-xl": "0px 8px 16px -4px hsl(0 0% 0% / 0.08), 0px 8px 10px -5px hsl(0 0% 0% / 0.08)", "shadow-2xl": "0px 8px 16px -4px hsl(0 0% 0% / 0.20)"}}}, {"name": "clean-slate", "type": "registry:style", "title": "Clean Slate", "description": "A theme based on the Clean Slate color palette.", "css": {"@layer base": {"body": {"letter-spacing": "var(--tracking-normal)"}}}, "cssVars": {"theme": {"font-sans": "Inter, sans-serif", "font-mono": "JetBrains Mono, monospace", "font-serif": "<PERSON><PERSON><PERSON><PERSON>, serif", "radius": "0.5rem", "tracking-tighter": "calc(var(--tracking-normal) - 0.05em)", "tracking-tight": "calc(var(--tracking-normal) - 0.025em)", "tracking-wide": "calc(var(--tracking-normal) + 0.025em)", "tracking-wider": "calc(var(--tracking-normal) + 0.05em)", "tracking-widest": "calc(var(--tracking-normal) + 0.1em)"}, "light": {"background": "oklch(0.98 0.00 247.86)", "foreground": "oklch(0.28 0.04 260.03)", "card": "oklch(1.00 0 0)", "card-foreground": "oklch(0.28 0.04 260.03)", "popover": "oklch(1.00 0 0)", "popover-foreground": "oklch(0.28 0.04 260.03)", "primary": "oklch(0.59 0.20 277.12)", "primary-foreground": "oklch(1.00 0 0)", "secondary": "oklch(0.93 0.01 264.53)", "secondary-foreground": "oklch(0.37 0.03 259.73)", "muted": "oklch(0.97 0.00 264.54)", "muted-foreground": "oklch(0.55 0.02 264.36)", "accent": "oklch(0.93 0.03 272.79)", "accent-foreground": "oklch(0.37 0.03 259.73)", "destructive": "oklch(0.64 0.21 25.33)", "destructive-foreground": "oklch(1.00 0 0)", "border": "oklch(0.87 0.01 258.34)", "input": "oklch(0.87 0.01 258.34)", "ring": "oklch(0.59 0.20 277.12)", "chart-1": "oklch(0.59 0.20 277.12)", "chart-2": "oklch(0.51 0.23 276.97)", "chart-3": "oklch(0.46 0.21 277.02)", "chart-4": "oklch(0.40 0.18 277.37)", "chart-5": "oklch(0.36 0.14 278.70)", "radius": "0.5rem", "sidebar": "oklch(0.97 0.00 264.54)", "sidebar-foreground": "oklch(0.28 0.04 260.03)", "sidebar-primary": "oklch(0.59 0.20 277.12)", "sidebar-primary-foreground": "oklch(1.00 0 0)", "sidebar-accent": "oklch(0.93 0.03 272.79)", "sidebar-accent-foreground": "oklch(0.37 0.03 259.73)", "sidebar-border": "oklch(0.87 0.01 258.34)", "sidebar-ring": "oklch(0.59 0.20 277.12)", "font-sans": "Inter, sans-serif", "font-serif": "<PERSON><PERSON><PERSON><PERSON>, serif", "font-mono": "JetBrains Mono, monospace", "shadow-color": "hsl(0 0% 0%)", "shadow-opacity": "0.1", "shadow-blur": "8px", "shadow-spread": "-1px", "shadow-offset-x": "0px", "shadow-offset-y": "4px", "letter-spacing": "0em", "spacing": "0.25rem", "shadow-2xs": "0px 4px 8px -1px hsl(0 0% 0% / 0.05)", "shadow-xs": "0px 4px 8px -1px hsl(0 0% 0% / 0.05)", "shadow-sm": "0px 4px 8px -1px hsl(0 0% 0% / 0.10), 0px 1px 2px -2px hsl(0 0% 0% / 0.10)", "shadow": "0px 4px 8px -1px hsl(0 0% 0% / 0.10), 0px 1px 2px -2px hsl(0 0% 0% / 0.10)", "shadow-md": "0px 4px 8px -1px hsl(0 0% 0% / 0.10), 0px 2px 4px -2px hsl(0 0% 0% / 0.10)", "shadow-lg": "0px 4px 8px -1px hsl(0 0% 0% / 0.10), 0px 4px 6px -2px hsl(0 0% 0% / 0.10)", "shadow-xl": "0px 4px 8px -1px hsl(0 0% 0% / 0.10), 0px 8px 10px -2px hsl(0 0% 0% / 0.10)", "shadow-2xl": "0px 4px 8px -1px hsl(0 0% 0% / 0.25)", "tracking-normal": "0em"}, "dark": {"background": "oklch(0.21 0.04 265.75)", "foreground": "oklch(0.93 0.01 255.51)", "card": "oklch(0.28 0.04 260.03)", "card-foreground": "oklch(0.93 0.01 255.51)", "popover": "oklch(0.28 0.04 260.03)", "popover-foreground": "oklch(0.93 0.01 255.51)", "primary": "oklch(0.68 0.16 276.93)", "primary-foreground": "oklch(0.21 0.04 265.75)", "secondary": "oklch(0.34 0.03 260.91)", "secondary-foreground": "oklch(0.87 0.01 258.34)", "muted": "oklch(0.28 0.04 260.03)", "muted-foreground": "oklch(0.71 0.02 261.32)", "accent": "oklch(0.37 0.03 259.73)", "accent-foreground": "oklch(0.87 0.01 258.34)", "destructive": "oklch(0.64 0.21 25.33)", "destructive-foreground": "oklch(0.21 0.04 265.75)", "border": "oklch(0.45 0.03 256.80)", "input": "oklch(0.45 0.03 256.80)", "ring": "oklch(0.68 0.16 276.93)", "chart-1": "oklch(0.68 0.16 276.93)", "chart-2": "oklch(0.59 0.20 277.12)", "chart-3": "oklch(0.51 0.23 276.97)", "chart-4": "oklch(0.46 0.21 277.02)", "chart-5": "oklch(0.40 0.18 277.37)", "radius": "0.5rem", "sidebar": "oklch(0.28 0.04 260.03)", "sidebar-foreground": "oklch(0.93 0.01 255.51)", "sidebar-primary": "oklch(0.68 0.16 276.93)", "sidebar-primary-foreground": "oklch(0.21 0.04 265.75)", "sidebar-accent": "oklch(0.37 0.03 259.73)", "sidebar-accent-foreground": "oklch(0.87 0.01 258.34)", "sidebar-border": "oklch(0.45 0.03 256.80)", "sidebar-ring": "oklch(0.68 0.16 276.93)", "font-sans": "Inter, sans-serif", "font-serif": "<PERSON><PERSON><PERSON><PERSON>, serif", "font-mono": "JetBrains Mono, monospace", "shadow-color": "hsl(0 0% 0%)", "shadow-opacity": "0.1", "shadow-blur": "8px", "shadow-spread": "-1px", "shadow-offset-x": "0px", "shadow-offset-y": "4px", "letter-spacing": "0em", "spacing": "0.25rem", "shadow-2xs": "0px 4px 8px -1px hsl(0 0% 0% / 0.05)", "shadow-xs": "0px 4px 8px -1px hsl(0 0% 0% / 0.05)", "shadow-sm": "0px 4px 8px -1px hsl(0 0% 0% / 0.10), 0px 1px 2px -2px hsl(0 0% 0% / 0.10)", "shadow": "0px 4px 8px -1px hsl(0 0% 0% / 0.10), 0px 1px 2px -2px hsl(0 0% 0% / 0.10)", "shadow-md": "0px 4px 8px -1px hsl(0 0% 0% / 0.10), 0px 2px 4px -2px hsl(0 0% 0% / 0.10)", "shadow-lg": "0px 4px 8px -1px hsl(0 0% 0% / 0.10), 0px 4px 6px -2px hsl(0 0% 0% / 0.10)", "shadow-xl": "0px 4px 8px -1px hsl(0 0% 0% / 0.10), 0px 8px 10px -2px hsl(0 0% 0% / 0.10)", "shadow-2xl": "0px 4px 8px -1px hsl(0 0% 0% / 0.25)"}}}, {"name": "caffeine", "type": "registry:style", "title": "Caffeine", "description": "A theme based on the Caffeine color palette.", "css": {"@layer base": {"body": {"letter-spacing": "var(--tracking-normal)"}}}, "cssVars": {"theme": {"font-sans": "ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', <PERSON>l, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji'", "font-mono": "ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, \"Liberation Mono\", \"Courier New\", monospace", "font-serif": "ui-serif, Georgia, Cambria, \"Times New Roman\", Times, serif", "radius": "0.5rem", "tracking-tighter": "calc(var(--tracking-normal) - 0.05em)", "tracking-tight": "calc(var(--tracking-normal) - 0.025em)", "tracking-wide": "calc(var(--tracking-normal) + 0.025em)", "tracking-wider": "calc(var(--tracking-normal) + 0.05em)", "tracking-widest": "calc(var(--tracking-normal) + 0.1em)"}, "light": {"background": "oklch(0.98 0 0)", "foreground": "oklch(0.24 0 0)", "card": "oklch(0.99 0 0)", "card-foreground": "oklch(0.24 0 0)", "popover": "oklch(0.99 0 0)", "popover-foreground": "oklch(0.24 0 0)", "primary": "oklch(0.43 0.04 41.99)", "primary-foreground": "oklch(1.00 0 0)", "secondary": "oklch(0.92 0.07 74.37)", "secondary-foreground": "oklch(0.35 0.07 40.83)", "muted": "oklch(0.95 0 0)", "muted-foreground": "oklch(0.50 0 0)", "accent": "oklch(0.93 0 0)", "accent-foreground": "oklch(0.24 0 0)", "destructive": "oklch(0.63 0.19 33.34)", "destructive-foreground": "oklch(1.00 0 0)", "border": "oklch(0.88 0 0)", "input": "oklch(0.88 0 0)", "ring": "oklch(0.43 0.04 41.99)", "chart-1": "oklch(0.43 0.04 41.99)", "chart-2": "oklch(0.92 0.07 74.37)", "chart-3": "oklch(0.93 0 0)", "chart-4": "oklch(0.94 0.05 75.50)", "chart-5": "oklch(0.43 0.04 41.67)", "radius": "0.5rem", "sidebar": "oklch(0.99 0 0)", "sidebar-foreground": "oklch(0.26 0 0)", "sidebar-primary": "oklch(0.33 0 0)", "sidebar-primary-foreground": "oklch(0.99 0 0)", "sidebar-accent": "oklch(0.98 0 0)", "sidebar-accent-foreground": "oklch(0.33 0 0)", "sidebar-border": "oklch(0.94 0 0)", "sidebar-ring": "oklch(0.77 0 0)", "font-sans": "ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', <PERSON>l, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji'", "font-serif": "ui-serif, Georgia, Cambria, \"Times New Roman\", Times, serif", "font-mono": "ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, \"Liberation Mono\", \"Courier New\", monospace", "shadow-color": "hsl(0 0% 0%)", "shadow-opacity": "0.1", "shadow-blur": "3px", "shadow-spread": "0px", "shadow-offset-x": "0", "shadow-offset-y": "1px", "letter-spacing": "0em", "spacing": "0.25rem", "shadow-2xs": "0 1px 3px 0px hsl(0 0% 0% / 0.05)", "shadow-xs": "0 1px 3px 0px hsl(0 0% 0% / 0.05)", "shadow-sm": "0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 1px 2px -1px hsl(0 0% 0% / 0.10)", "shadow": "0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 1px 2px -1px hsl(0 0% 0% / 0.10)", "shadow-md": "0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 2px 4px -1px hsl(0 0% 0% / 0.10)", "shadow-lg": "0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 4px 6px -1px hsl(0 0% 0% / 0.10)", "shadow-xl": "0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 8px 10px -1px hsl(0 0% 0% / 0.10)", "shadow-2xl": "0 1px 3px 0px hsl(0 0% 0% / 0.25)", "tracking-normal": "0em"}, "dark": {"background": "oklch(0.18 0 0)", "foreground": "oklch(0.95 0 0)", "card": "oklch(0.21 0 0)", "card-foreground": "oklch(0.95 0 0)", "popover": "oklch(0.21 0 0)", "popover-foreground": "oklch(0.95 0 0)", "primary": "oklch(0.92 0.05 66.17)", "primary-foreground": "oklch(0.20 0.02 200.20)", "secondary": "oklch(0.32 0.02 63.70)", "secondary-foreground": "oklch(0.92 0.05 66.17)", "muted": "oklch(0.25 0 0)", "muted-foreground": "oklch(0.77 0 0)", "accent": "oklch(0.29 0 0)", "accent-foreground": "oklch(0.95 0 0)", "destructive": "oklch(0.63 0.19 33.34)", "destructive-foreground": "oklch(1.00 0 0)", "border": "oklch(0.24 0.01 91.75)", "input": "oklch(0.40 0 0)", "ring": "oklch(0.92 0.05 66.17)", "chart-1": "oklch(0.92 0.05 66.17)", "chart-2": "oklch(0.32 0.02 63.70)", "chart-3": "oklch(0.29 0 0)", "chart-4": "oklch(0.35 0.02 67.00)", "chart-5": "oklch(0.92 0.05 67.09)", "radius": "0.5rem", "sidebar": "oklch(0.21 0.01 285.89)", "sidebar-foreground": "oklch(0.97 0.00 286.38)", "sidebar-primary": "oklch(0.49 0.22 264.38)", "sidebar-primary-foreground": "oklch(1.00 0 0)", "sidebar-accent": "oklch(0.27 0.01 286.03)", "sidebar-accent-foreground": "oklch(0.97 0.00 286.38)", "sidebar-border": "oklch(0.27 0.01 286.03)", "sidebar-ring": "oklch(0.87 0.01 286.29)", "font-sans": "ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', <PERSON>l, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji'", "font-serif": "ui-serif, Georgia, Cambria, \"Times New Roman\", Times, serif", "font-mono": "ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, \"Liberation Mono\", \"Courier New\", monospace", "shadow-color": "hsl(0 0% 0%)", "shadow-opacity": "0.1", "shadow-blur": "3px", "shadow-spread": "0px", "shadow-offset-x": "0", "shadow-offset-y": "1px", "letter-spacing": "0em", "spacing": "0.25rem", "shadow-2xs": "0 1px 3px 0px hsl(0 0% 0% / 0.05)", "shadow-xs": "0 1px 3px 0px hsl(0 0% 0% / 0.05)", "shadow-sm": "0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 1px 2px -1px hsl(0 0% 0% / 0.10)", "shadow": "0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 1px 2px -1px hsl(0 0% 0% / 0.10)", "shadow-md": "0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 2px 4px -1px hsl(0 0% 0% / 0.10)", "shadow-lg": "0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 4px 6px -1px hsl(0 0% 0% / 0.10)", "shadow-xl": "0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 8px 10px -1px hsl(0 0% 0% / 0.10)", "shadow-2xl": "0 1px 3px 0px hsl(0 0% 0% / 0.25)"}}}, {"name": "ocean-breeze", "type": "registry:style", "title": "Ocean Breeze", "description": "A theme based on the Ocean Breeze color palette.", "css": {"@layer base": {"body": {"letter-spacing": "var(--tracking-normal)"}}}, "cssVars": {"theme": {"font-sans": "DM Sans, sans-serif", "font-mono": "IBM Plex Mono, monospace", "font-serif": "Lo<PERSON>, serif", "radius": "0.5rem", "tracking-tighter": "calc(var(--tracking-normal) - 0.05em)", "tracking-tight": "calc(var(--tracking-normal) - 0.025em)", "tracking-wide": "calc(var(--tracking-normal) + 0.025em)", "tracking-wider": "calc(var(--tracking-normal) + 0.05em)", "tracking-widest": "calc(var(--tracking-normal) + 0.1em)"}, "light": {"background": "oklch(0.98 0.01 244.25)", "foreground": "oklch(0.37 0.03 259.73)", "card": "oklch(1.00 0 0)", "card-foreground": "oklch(0.37 0.03 259.73)", "popover": "oklch(1.00 0 0)", "popover-foreground": "oklch(0.37 0.03 259.73)", "primary": "oklch(0.72 0.19 149.58)", "primary-foreground": "oklch(1.00 0 0)", "secondary": "oklch(0.95 0.03 236.82)", "secondary-foreground": "oklch(0.45 0.03 256.80)", "muted": "oklch(0.97 0.00 264.54)", "muted-foreground": "oklch(0.55 0.02 264.36)", "accent": "oklch(0.95 0.05 163.05)", "accent-foreground": "oklch(0.37 0.03 259.73)", "destructive": "oklch(0.64 0.21 25.33)", "destructive-foreground": "oklch(1.00 0 0)", "border": "oklch(0.93 0.01 264.53)", "input": "oklch(0.93 0.01 264.53)", "ring": "oklch(0.72 0.19 149.58)", "chart-1": "oklch(0.72 0.19 149.58)", "chart-2": "oklch(0.70 0.15 162.48)", "chart-3": "oklch(0.60 0.13 163.23)", "chart-4": "oklch(0.51 0.10 165.61)", "chart-5": "oklch(0.43 0.09 166.91)", "radius": "0.5rem", "sidebar": "oklch(0.95 0.03 236.82)", "sidebar-foreground": "oklch(0.37 0.03 259.73)", "sidebar-primary": "oklch(0.72 0.19 149.58)", "sidebar-primary-foreground": "oklch(1.00 0 0)", "sidebar-accent": "oklch(0.95 0.05 163.05)", "sidebar-accent-foreground": "oklch(0.37 0.03 259.73)", "sidebar-border": "oklch(0.93 0.01 264.53)", "sidebar-ring": "oklch(0.72 0.19 149.58)", "font-sans": "DM Sans, sans-serif", "font-serif": "Lo<PERSON>, serif", "font-mono": "IBM Plex Mono, monospace", "shadow-color": "hsl(0 0% 0%)", "shadow-opacity": "0.1", "shadow-blur": "8px", "shadow-spread": "-1px", "shadow-offset-x": "0px", "shadow-offset-y": "4px", "letter-spacing": "0em", "spacing": "0.25rem", "shadow-2xs": "0px 4px 8px -1px hsl(0 0% 0% / 0.05)", "shadow-xs": "0px 4px 8px -1px hsl(0 0% 0% / 0.05)", "shadow-sm": "0px 4px 8px -1px hsl(0 0% 0% / 0.10), 0px 1px 2px -2px hsl(0 0% 0% / 0.10)", "shadow": "0px 4px 8px -1px hsl(0 0% 0% / 0.10), 0px 1px 2px -2px hsl(0 0% 0% / 0.10)", "shadow-md": "0px 4px 8px -1px hsl(0 0% 0% / 0.10), 0px 2px 4px -2px hsl(0 0% 0% / 0.10)", "shadow-lg": "0px 4px 8px -1px hsl(0 0% 0% / 0.10), 0px 4px 6px -2px hsl(0 0% 0% / 0.10)", "shadow-xl": "0px 4px 8px -1px hsl(0 0% 0% / 0.10), 0px 8px 10px -2px hsl(0 0% 0% / 0.10)", "shadow-2xl": "0px 4px 8px -1px hsl(0 0% 0% / 0.25)", "tracking-normal": "0em"}, "dark": {"background": "oklch(0.21 0.04 265.75)", "foreground": "oklch(0.87 0.01 258.34)", "card": "oklch(0.28 0.04 260.03)", "card-foreground": "oklch(0.87 0.01 258.34)", "popover": "oklch(0.28 0.04 260.03)", "popover-foreground": "oklch(0.87 0.01 258.34)", "primary": "oklch(0.77 0.15 163.22)", "primary-foreground": "oklch(0.21 0.04 265.75)", "secondary": "oklch(0.34 0.03 260.91)", "secondary-foreground": "oklch(0.71 0.01 286.07)", "muted": "oklch(0.28 0.04 260.03)", "muted-foreground": "oklch(0.55 0.02 264.36)", "accent": "oklch(0.37 0.03 259.73)", "accent-foreground": "oklch(0.71 0.01 286.07)", "destructive": "oklch(0.64 0.21 25.33)", "destructive-foreground": "oklch(0.21 0.04 265.75)", "border": "oklch(0.45 0.03 256.80)", "input": "oklch(0.45 0.03 256.80)", "ring": "oklch(0.77 0.15 163.22)", "chart-1": "oklch(0.77 0.15 163.22)", "chart-2": "oklch(0.78 0.13 181.91)", "chart-3": "oklch(0.72 0.19 149.58)", "chart-4": "oklch(0.70 0.15 162.48)", "chart-5": "oklch(0.60 0.13 163.23)", "radius": "0.5rem", "sidebar": "oklch(0.28 0.04 260.03)", "sidebar-foreground": "oklch(0.87 0.01 258.34)", "sidebar-primary": "oklch(0.77 0.15 163.22)", "sidebar-primary-foreground": "oklch(0.21 0.04 265.75)", "sidebar-accent": "oklch(0.37 0.03 259.73)", "sidebar-accent-foreground": "oklch(0.71 0.01 286.07)", "sidebar-border": "oklch(0.45 0.03 256.80)", "sidebar-ring": "oklch(0.77 0.15 163.22)", "font-sans": "DM Sans, sans-serif", "font-serif": "Lo<PERSON>, serif", "font-mono": "IBM Plex Mono, monospace", "shadow-color": "hsl(0 0% 0%)", "shadow-opacity": "0.1", "shadow-blur": "8px", "shadow-spread": "-1px", "shadow-offset-x": "0px", "shadow-offset-y": "4px", "letter-spacing": "0em", "spacing": "0.25rem", "shadow-2xs": "0px 4px 8px -1px hsl(0 0% 0% / 0.05)", "shadow-xs": "0px 4px 8px -1px hsl(0 0% 0% / 0.05)", "shadow-sm": "0px 4px 8px -1px hsl(0 0% 0% / 0.10), 0px 1px 2px -2px hsl(0 0% 0% / 0.10)", "shadow": "0px 4px 8px -1px hsl(0 0% 0% / 0.10), 0px 1px 2px -2px hsl(0 0% 0% / 0.10)", "shadow-md": "0px 4px 8px -1px hsl(0 0% 0% / 0.10), 0px 2px 4px -2px hsl(0 0% 0% / 0.10)", "shadow-lg": "0px 4px 8px -1px hsl(0 0% 0% / 0.10), 0px 4px 6px -2px hsl(0 0% 0% / 0.10)", "shadow-xl": "0px 4px 8px -1px hsl(0 0% 0% / 0.10), 0px 8px 10px -2px hsl(0 0% 0% / 0.10)", "shadow-2xl": "0px 4px 8px -1px hsl(0 0% 0% / 0.25)"}}}, {"name": "retro-arcade", "type": "registry:style", "title": "Retro Arcade", "description": "A theme based on the Retro Arcade color palette.", "css": {"@layer base": {"body": {"letter-spacing": "var(--tracking-normal)"}}}, "cssVars": {"theme": {"font-sans": "Outfit, sans-serif", "font-mono": "Space Mono, monospace", "font-serif": "ui-serif, Georgia, Cambria, \"Times New Roman\", Times, serif", "radius": "0.25rem", "tracking-tighter": "calc(var(--tracking-normal) - 0.05em)", "tracking-tight": "calc(var(--tracking-normal) - 0.025em)", "tracking-wide": "calc(var(--tracking-normal) + 0.025em)", "tracking-wider": "calc(var(--tracking-normal) + 0.05em)", "tracking-widest": "calc(var(--tracking-normal) + 0.1em)"}, "light": {"background": "oklch(0.97 0.03 90.10)", "foreground": "oklch(0.31 0.05 219.65)", "card": "oklch(0.93 0.03 92.40)", "card-foreground": "oklch(0.31 0.05 219.65)", "popover": "oklch(0.93 0.03 92.40)", "popover-foreground": "oklch(0.31 0.05 219.65)", "primary": "oklch(0.59 0.20 355.89)", "primary-foreground": "oklch(1.00 0 0)", "secondary": "oklch(0.64 0.10 187.38)", "secondary-foreground": "oklch(1.00 0 0)", "muted": "oklch(0.70 0.02 196.79)", "muted-foreground": "oklch(0.31 0.05 219.65)", "accent": "oklch(0.58 0.17 39.50)", "accent-foreground": "oklch(1.00 0 0)", "destructive": "oklch(0.59 0.21 27.12)", "destructive-foreground": "oklch(1.00 0 0)", "border": "oklch(0.65 0.02 205.26)", "input": "oklch(0.65 0.02 205.26)", "ring": "oklch(0.59 0.20 355.89)", "chart-1": "oklch(0.61 0.14 244.93)", "chart-2": "oklch(0.64 0.10 187.38)", "chart-3": "oklch(0.59 0.20 355.89)", "chart-4": "oklch(0.58 0.17 39.50)", "chart-5": "oklch(0.59 0.21 27.12)", "radius": "0.25rem", "sidebar": "oklch(0.97 0.03 90.10)", "sidebar-foreground": "oklch(0.31 0.05 219.65)", "sidebar-primary": "oklch(0.59 0.20 355.89)", "sidebar-primary-foreground": "oklch(1.00 0 0)", "sidebar-accent": "oklch(0.64 0.10 187.38)", "sidebar-accent-foreground": "oklch(1.00 0 0)", "sidebar-border": "oklch(0.65 0.02 205.26)", "sidebar-ring": "oklch(0.59 0.20 355.89)", "font-sans": "Outfit, sans-serif", "font-serif": "ui-serif, Georgia, Cambria, \"Times New Roman\", Times, serif", "font-mono": "Space Mono, monospace", "shadow-color": "hsl(196 83% 10%)", "shadow-opacity": "0.15", "shadow-blur": "4px", "shadow-spread": "0px", "shadow-offset-x": "2px", "shadow-offset-y": "2px", "letter-spacing": "0em", "spacing": "0.25rem", "shadow-2xs": "2px 2px 4px 0px hsl(196 83% 10% / 0.07)", "shadow-xs": "2px 2px 4px 0px hsl(196 83% 10% / 0.07)", "shadow-sm": "2px 2px 4px 0px hsl(196 83% 10% / 0.15), 2px 1px 2px -1px hsl(196 83% 10% / 0.15)", "shadow": "2px 2px 4px 0px hsl(196 83% 10% / 0.15), 2px 1px 2px -1px hsl(196 83% 10% / 0.15)", "shadow-md": "2px 2px 4px 0px hsl(196 83% 10% / 0.15), 2px 2px 4px -1px hsl(196 83% 10% / 0.15)", "shadow-lg": "2px 2px 4px 0px hsl(196 83% 10% / 0.15), 2px 4px 6px -1px hsl(196 83% 10% / 0.15)", "shadow-xl": "2px 2px 4px 0px hsl(196 83% 10% / 0.15), 2px 8px 10px -1px hsl(196 83% 10% / 0.15)", "shadow-2xl": "2px 2px 4px 0px hsl(196 83% 10% / 0.38)", "tracking-normal": "0em"}, "dark": {"background": "oklch(0.27 0.05 219.82)", "foreground": "oklch(0.70 0.02 196.79)", "card": "oklch(0.31 0.05 219.65)", "card-foreground": "oklch(0.70 0.02 196.79)", "popover": "oklch(0.31 0.05 219.65)", "popover-foreground": "oklch(0.70 0.02 196.79)", "primary": "oklch(0.59 0.20 355.89)", "primary-foreground": "oklch(1.00 0 0)", "secondary": "oklch(0.64 0.10 187.38)", "secondary-foreground": "oklch(1.00 0 0)", "muted": "oklch(0.52 0.03 219.14)", "muted-foreground": "oklch(0.70 0.02 196.79)", "accent": "oklch(0.58 0.17 39.50)", "accent-foreground": "oklch(1.00 0 0)", "destructive": "oklch(0.59 0.21 27.12)", "destructive-foreground": "oklch(1.00 0 0)", "border": "oklch(0.52 0.03 219.14)", "input": "oklch(0.52 0.03 219.14)", "ring": "oklch(0.59 0.20 355.89)", "chart-1": "oklch(0.61 0.14 244.93)", "chart-2": "oklch(0.64 0.10 187.38)", "chart-3": "oklch(0.59 0.20 355.89)", "chart-4": "oklch(0.58 0.17 39.50)", "chart-5": "oklch(0.59 0.21 27.12)", "radius": "0.25rem", "sidebar": "oklch(0.27 0.05 219.82)", "sidebar-foreground": "oklch(0.70 0.02 196.79)", "sidebar-primary": "oklch(0.59 0.20 355.89)", "sidebar-primary-foreground": "oklch(1.00 0 0)", "sidebar-accent": "oklch(0.64 0.10 187.38)", "sidebar-accent-foreground": "oklch(1.00 0 0)", "sidebar-border": "oklch(0.52 0.03 219.14)", "sidebar-ring": "oklch(0.59 0.20 355.89)", "font-sans": "Outfit, sans-serif", "font-serif": "ui-serif, Georgia, Cambria, \"Times New Roman\", Times, serif", "font-mono": "Space Mono, monospace", "shadow-color": "hsl(196 83% 10%)", "shadow-opacity": "0.15", "shadow-blur": "4px", "shadow-spread": "0px", "shadow-offset-x": "2px", "shadow-offset-y": "2px", "letter-spacing": "0em", "spacing": "0.25rem", "shadow-2xs": "2px 2px 4px 0px hsl(196 83% 10% / 0.07)", "shadow-xs": "2px 2px 4px 0px hsl(196 83% 10% / 0.07)", "shadow-sm": "2px 2px 4px 0px hsl(196 83% 10% / 0.15), 2px 1px 2px -1px hsl(196 83% 10% / 0.15)", "shadow": "2px 2px 4px 0px hsl(196 83% 10% / 0.15), 2px 1px 2px -1px hsl(196 83% 10% / 0.15)", "shadow-md": "2px 2px 4px 0px hsl(196 83% 10% / 0.15), 2px 2px 4px -1px hsl(196 83% 10% / 0.15)", "shadow-lg": "2px 2px 4px 0px hsl(196 83% 10% / 0.15), 2px 4px 6px -1px hsl(196 83% 10% / 0.15)", "shadow-xl": "2px 2px 4px 0px hsl(196 83% 10% / 0.15), 2px 8px 10px -1px hsl(196 83% 10% / 0.15)", "shadow-2xl": "2px 2px 4px 0px hsl(196 83% 10% / 0.38)"}}}, {"name": "midnight-bloom", "type": "registry:style", "title": "Midnight Bloom", "description": "A theme based on the Midnight Bloom color palette.", "css": {"@layer base": {"body": {"letter-spacing": "var(--tracking-normal)"}}}, "cssVars": {"theme": {"font-sans": "Montserrat, sans-serif", "font-mono": "Source Code Pro, monospace", "font-serif": "Playfair Display, serif", "radius": "0.5rem", "tracking-tighter": "calc(var(--tracking-normal) - 0.05em)", "tracking-tight": "calc(var(--tracking-normal) - 0.025em)", "tracking-wide": "calc(var(--tracking-normal) + 0.025em)", "tracking-wider": "calc(var(--tracking-normal) + 0.05em)", "tracking-widest": "calc(var(--tracking-normal) + 0.1em)"}, "light": {"background": "oklch(0.98 0 0)", "foreground": "oklch(0.32 0 0)", "card": "oklch(1.00 0 0)", "card-foreground": "oklch(0.32 0 0)", "popover": "oklch(1.00 0 0)", "popover-foreground": "oklch(0.32 0 0)", "primary": "oklch(0.57 0.20 283.08)", "primary-foreground": "oklch(1.00 0 0)", "secondary": "oklch(0.82 0.07 249.35)", "secondary-foreground": "oklch(0.32 0 0)", "muted": "oklch(0.82 0.02 91.62)", "muted-foreground": "oklch(0.54 0 0)", "accent": "oklch(0.65 0.06 117.43)", "accent-foreground": "oklch(1.00 0 0)", "destructive": "oklch(0.64 0.21 25.33)", "destructive-foreground": "oklch(1.00 0 0)", "border": "oklch(0.87 0 0)", "input": "oklch(0.87 0 0)", "ring": "oklch(0.57 0.20 283.08)", "chart-1": "oklch(0.57 0.20 283.08)", "chart-2": "oklch(0.53 0.17 314.65)", "chart-3": "oklch(0.34 0.18 301.68)", "chart-4": "oklch(0.67 0.14 261.34)", "chart-5": "oklch(0.59 0.10 245.74)", "radius": "0.5rem", "sidebar": "oklch(0.98 0 0)", "sidebar-foreground": "oklch(0.32 0 0)", "sidebar-primary": "oklch(0.57 0.20 283.08)", "sidebar-primary-foreground": "oklch(1.00 0 0)", "sidebar-accent": "oklch(0.65 0.06 117.43)", "sidebar-accent-foreground": "oklch(1.00 0 0)", "sidebar-border": "oklch(0.87 0 0)", "sidebar-ring": "oklch(0.57 0.20 283.08)", "font-sans": "Montserrat, sans-serif", "font-serif": "Playfair Display, serif", "font-mono": "Source Code Pro, monospace", "shadow-color": "hsl(0 0% 0%)", "shadow-opacity": "0.1", "shadow-blur": "10px", "shadow-spread": "-2px", "shadow-offset-x": "0px", "shadow-offset-y": "5px", "letter-spacing": "0em", "spacing": "0.25rem", "shadow-2xs": "0px 5px 10px -2px hsl(0 0% 0% / 0.05)", "shadow-xs": "0px 5px 10px -2px hsl(0 0% 0% / 0.05)", "shadow-sm": "0px 5px 10px -2px hsl(0 0% 0% / 0.10), 0px 1px 2px -3px hsl(0 0% 0% / 0.10)", "shadow": "0px 5px 10px -2px hsl(0 0% 0% / 0.10), 0px 1px 2px -3px hsl(0 0% 0% / 0.10)", "shadow-md": "0px 5px 10px -2px hsl(0 0% 0% / 0.10), 0px 2px 4px -3px hsl(0 0% 0% / 0.10)", "shadow-lg": "0px 5px 10px -2px hsl(0 0% 0% / 0.10), 0px 4px 6px -3px hsl(0 0% 0% / 0.10)", "shadow-xl": "0px 5px 10px -2px hsl(0 0% 0% / 0.10), 0px 8px 10px -3px hsl(0 0% 0% / 0.10)", "shadow-2xl": "0px 5px 10px -2px hsl(0 0% 0% / 0.25)", "tracking-normal": "0em"}, "dark": {"background": "oklch(0.23 0.01 264.29)", "foreground": "oklch(0.92 0 0)", "card": "oklch(0.32 0.01 223.67)", "card-foreground": "oklch(0.92 0 0)", "popover": "oklch(0.32 0.01 223.67)", "popover-foreground": "oklch(0.92 0 0)", "primary": "oklch(0.57 0.20 283.08)", "primary-foreground": "oklch(1.00 0 0)", "secondary": "oklch(0.34 0.18 301.68)", "secondary-foreground": "oklch(0.92 0 0)", "muted": "oklch(0.39 0 0)", "muted-foreground": "oklch(0.72 0 0)", "accent": "oklch(0.67 0.14 261.34)", "accent-foreground": "oklch(0.92 0 0)", "destructive": "oklch(0.64 0.21 25.33)", "destructive-foreground": "oklch(1.00 0 0)", "border": "oklch(0.39 0 0)", "input": "oklch(0.39 0 0)", "ring": "oklch(0.57 0.20 283.08)", "chart-1": "oklch(0.57 0.20 283.08)", "chart-2": "oklch(0.53 0.17 314.65)", "chart-3": "oklch(0.34 0.18 301.68)", "chart-4": "oklch(0.67 0.14 261.34)", "chart-5": "oklch(0.59 0.10 245.74)", "radius": "0.5rem", "sidebar": "oklch(0.23 0.01 264.29)", "sidebar-foreground": "oklch(0.92 0 0)", "sidebar-primary": "oklch(0.57 0.20 283.08)", "sidebar-primary-foreground": "oklch(1.00 0 0)", "sidebar-accent": "oklch(0.67 0.14 261.34)", "sidebar-accent-foreground": "oklch(0.92 0 0)", "sidebar-border": "oklch(0.39 0 0)", "sidebar-ring": "oklch(0.57 0.20 283.08)", "font-sans": "Montserrat, sans-serif", "font-serif": "Playfair Display, serif", "font-mono": "Source Code Pro, monospace", "shadow-color": "hsl(0 0% 0%)", "shadow-opacity": "0.1", "shadow-blur": "10px", "shadow-spread": "-2px", "shadow-offset-x": "0px", "shadow-offset-y": "5px", "letter-spacing": "0em", "spacing": "0.25rem", "shadow-2xs": "0px 5px 10px -2px hsl(0 0% 0% / 0.05)", "shadow-xs": "0px 5px 10px -2px hsl(0 0% 0% / 0.05)", "shadow-sm": "0px 5px 10px -2px hsl(0 0% 0% / 0.10), 0px 1px 2px -3px hsl(0 0% 0% / 0.10)", "shadow": "0px 5px 10px -2px hsl(0 0% 0% / 0.10), 0px 1px 2px -3px hsl(0 0% 0% / 0.10)", "shadow-md": "0px 5px 10px -2px hsl(0 0% 0% / 0.10), 0px 2px 4px -3px hsl(0 0% 0% / 0.10)", "shadow-lg": "0px 5px 10px -2px hsl(0 0% 0% / 0.10), 0px 4px 6px -3px hsl(0 0% 0% / 0.10)", "shadow-xl": "0px 5px 10px -2px hsl(0 0% 0% / 0.10), 0px 8px 10px -3px hsl(0 0% 0% / 0.10)", "shadow-2xl": "0px 5px 10px -2px hsl(0 0% 0% / 0.25)"}}}, {"name": "candyland", "type": "registry:style", "title": "Candyland", "description": "A theme based on the Candyland color palette.", "css": {"@layer base": {"body": {"letter-spacing": "var(--tracking-normal)"}}}, "cssVars": {"theme": {"font-sans": "<PERSON><PERSON><PERSON>, sans-serif", "font-mono": "Roboto Mono, monospace", "font-serif": "ui-serif, Georgia, Cambria, \"Times New Roman\", Times, serif", "radius": "0.5rem", "tracking-tighter": "calc(var(--tracking-normal) - 0.05em)", "tracking-tight": "calc(var(--tracking-normal) - 0.025em)", "tracking-wide": "calc(var(--tracking-normal) + 0.025em)", "tracking-wider": "calc(var(--tracking-normal) + 0.05em)", "tracking-widest": "calc(var(--tracking-normal) + 0.1em)"}, "light": {"background": "oklch(0.98 0.00 228.78)", "foreground": "oklch(0.32 0 0)", "card": "oklch(1.00 0 0)", "card-foreground": "oklch(0.32 0 0)", "popover": "oklch(1.00 0 0)", "popover-foreground": "oklch(0.32 0 0)", "primary": "oklch(0.87 0.07 7.09)", "primary-foreground": "oklch(0 0 0)", "secondary": "oklch(0.81 0.08 225.75)", "secondary-foreground": "oklch(0 0 0)", "muted": "oklch(0.88 0.03 98.10)", "muted-foreground": "oklch(0.54 0 0)", "accent": "oklch(0.97 0.21 109.77)", "accent-foreground": "oklch(0 0 0)", "destructive": "oklch(0.64 0.21 25.33)", "destructive-foreground": "oklch(1.00 0 0)", "border": "oklch(0.87 0 0)", "input": "oklch(0.87 0 0)", "ring": "oklch(0.87 0.07 7.09)", "chart-1": "oklch(0.87 0.07 7.09)", "chart-2": "oklch(0.81 0.08 225.75)", "chart-3": "oklch(0.97 0.21 109.77)", "chart-4": "oklch(0.80 0.14 349.23)", "chart-5": "oklch(0.74 0.23 142.85)", "radius": "0.5rem", "sidebar": "oklch(0.98 0.00 228.78)", "sidebar-foreground": "oklch(0.32 0 0)", "sidebar-primary": "oklch(0.87 0.07 7.09)", "sidebar-primary-foreground": "oklch(0 0 0)", "sidebar-accent": "oklch(0.97 0.21 109.77)", "sidebar-accent-foreground": "oklch(0 0 0)", "sidebar-border": "oklch(0.87 0 0)", "sidebar-ring": "oklch(0.87 0.07 7.09)", "font-sans": "<PERSON><PERSON><PERSON>, sans-serif", "font-serif": "ui-serif, Georgia, Cambria, \"Times New Roman\", Times, serif", "font-mono": "Roboto Mono, monospace", "shadow-color": "hsl(0 0% 0%)", "shadow-opacity": "0.1", "shadow-blur": "3px", "shadow-spread": "0px", "shadow-offset-x": "0", "shadow-offset-y": "1px", "letter-spacing": "0em", "spacing": "0.25rem", "shadow-2xs": "0 1px 3px 0px hsl(0 0% 0% / 0.05)", "shadow-xs": "0 1px 3px 0px hsl(0 0% 0% / 0.05)", "shadow-sm": "0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 1px 2px -1px hsl(0 0% 0% / 0.10)", "shadow": "0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 1px 2px -1px hsl(0 0% 0% / 0.10)", "shadow-md": "0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 2px 4px -1px hsl(0 0% 0% / 0.10)", "shadow-lg": "0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 4px 6px -1px hsl(0 0% 0% / 0.10)", "shadow-xl": "0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 8px 10px -1px hsl(0 0% 0% / 0.10)", "shadow-2xl": "0 1px 3px 0px hsl(0 0% 0% / 0.25)", "tracking-normal": "0em"}, "dark": {"background": "oklch(0.23 0.01 264.29)", "foreground": "oklch(0.92 0 0)", "card": "oklch(0.32 0.01 223.67)", "card-foreground": "oklch(0.92 0 0)", "popover": "oklch(0.32 0.01 223.67)", "popover-foreground": "oklch(0.92 0 0)", "primary": "oklch(0.80 0.14 349.23)", "primary-foreground": "oklch(0 0 0)", "secondary": "oklch(0.74 0.23 142.85)", "secondary-foreground": "oklch(0 0 0)", "muted": "oklch(0.39 0 0)", "muted-foreground": "oklch(0.72 0 0)", "accent": "oklch(0.81 0.08 225.75)", "accent-foreground": "oklch(0 0 0)", "destructive": "oklch(0.64 0.21 25.33)", "destructive-foreground": "oklch(1.00 0 0)", "border": "oklch(0.39 0 0)", "input": "oklch(0.39 0 0)", "ring": "oklch(0.80 0.14 349.23)", "chart-1": "oklch(0.80 0.14 349.23)", "chart-2": "oklch(0.74 0.23 142.85)", "chart-3": "oklch(0.81 0.08 225.75)", "chart-4": "oklch(0.97 0.21 109.77)", "chart-5": "oklch(0.87 0.18 90.38)", "radius": "0.5rem", "sidebar": "oklch(0.23 0.01 264.29)", "sidebar-foreground": "oklch(0.92 0 0)", "sidebar-primary": "oklch(0.80 0.14 349.23)", "sidebar-primary-foreground": "oklch(0 0 0)", "sidebar-accent": "oklch(0.81 0.08 225.75)", "sidebar-accent-foreground": "oklch(0 0 0)", "sidebar-border": "oklch(0.39 0 0)", "sidebar-ring": "oklch(0.80 0.14 349.23)", "font-sans": "<PERSON><PERSON><PERSON>, sans-serif", "font-serif": "ui-serif, Georgia, Cambria, \"Times New Roman\", Times, serif", "font-mono": "Roboto Mono, monospace", "shadow-color": "hsl(0 0% 0%)", "shadow-opacity": "0.1", "shadow-blur": "3px", "shadow-spread": "0px", "shadow-offset-x": "0", "shadow-offset-y": "1px", "letter-spacing": "0em", "spacing": "0.25rem", "shadow-2xs": "0 1px 3px 0px hsl(0 0% 0% / 0.05)", "shadow-xs": "0 1px 3px 0px hsl(0 0% 0% / 0.05)", "shadow-sm": "0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 1px 2px -1px hsl(0 0% 0% / 0.10)", "shadow": "0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 1px 2px -1px hsl(0 0% 0% / 0.10)", "shadow-md": "0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 2px 4px -1px hsl(0 0% 0% / 0.10)", "shadow-lg": "0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 4px 6px -1px hsl(0 0% 0% / 0.10)", "shadow-xl": "0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 8px 10px -1px hsl(0 0% 0% / 0.10)", "shadow-2xl": "0 1px 3px 0px hsl(0 0% 0% / 0.25)"}}}, {"name": "northern-lights", "type": "registry:style", "title": "Northern Lights", "description": "A theme based on the Northern Lights color palette.", "css": {"@layer base": {"body": {"letter-spacing": "var(--tracking-normal)"}}}, "cssVars": {"theme": {"font-sans": "Plus Jakarta Sans, sans-serif", "font-mono": "JetBrains Mono, monospace", "font-serif": "Source Serif 4, serif", "radius": "0.5rem", "tracking-tighter": "calc(var(--tracking-normal) - 0.05em)", "tracking-tight": "calc(var(--tracking-normal) - 0.025em)", "tracking-wide": "calc(var(--tracking-normal) + 0.025em)", "tracking-wider": "calc(var(--tracking-normal) + 0.05em)", "tracking-widest": "calc(var(--tracking-normal) + 0.1em)"}, "light": {"background": "oklch(0.98 0.00 286.38)", "foreground": "oklch(0.32 0 0)", "card": "oklch(1.00 0 0)", "card-foreground": "oklch(0.32 0 0)", "popover": "oklch(1.00 0 0)", "popover-foreground": "oklch(0.32 0 0)", "primary": "oklch(0.65 0.15 150.31)", "primary-foreground": "oklch(1.00 0 0)", "secondary": "oklch(0.67 0.14 261.34)", "secondary-foreground": "oklch(1.00 0 0)", "muted": "oklch(0.88 0.03 98.10)", "muted-foreground": "oklch(0.54 0 0)", "accent": "oklch(0.83 0.11 211.96)", "accent-foreground": "oklch(0.32 0 0)", "destructive": "oklch(0.64 0.21 25.33)", "destructive-foreground": "oklch(1.00 0 0)", "border": "oklch(0.87 0 0)", "input": "oklch(0.87 0 0)", "ring": "oklch(0.65 0.15 150.31)", "chart-1": "oklch(0.65 0.15 150.31)", "chart-2": "oklch(0.67 0.14 261.34)", "chart-3": "oklch(0.83 0.11 211.96)", "chart-4": "oklch(0.59 0.10 245.74)", "chart-5": "oklch(0.59 0.16 148.24)", "radius": "0.5rem", "sidebar": "oklch(0.98 0.00 286.38)", "sidebar-foreground": "oklch(0.32 0 0)", "sidebar-primary": "oklch(0.65 0.15 150.31)", "sidebar-primary-foreground": "oklch(1.00 0 0)", "sidebar-accent": "oklch(0.83 0.11 211.96)", "sidebar-accent-foreground": "oklch(0.32 0 0)", "sidebar-border": "oklch(0.87 0 0)", "sidebar-ring": "oklch(0.65 0.15 150.31)", "font-sans": "Plus Jakarta Sans, sans-serif", "font-serif": "Source Serif 4, serif", "font-mono": "JetBrains Mono, monospace", "shadow-color": "hsl(0 0% 0%)", "shadow-opacity": "0.1", "shadow-blur": "3px", "shadow-spread": "0px", "shadow-offset-x": "0", "shadow-offset-y": "1px", "letter-spacing": "0em", "spacing": "0.25rem", "shadow-2xs": "0 1px 3px 0px hsl(0 0% 0% / 0.05)", "shadow-xs": "0 1px 3px 0px hsl(0 0% 0% / 0.05)", "shadow-sm": "0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 1px 2px -1px hsl(0 0% 0% / 0.10)", "shadow": "0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 1px 2px -1px hsl(0 0% 0% / 0.10)", "shadow-md": "0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 2px 4px -1px hsl(0 0% 0% / 0.10)", "shadow-lg": "0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 4px 6px -1px hsl(0 0% 0% / 0.10)", "shadow-xl": "0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 8px 10px -1px hsl(0 0% 0% / 0.10)", "shadow-2xl": "0 1px 3px 0px hsl(0 0% 0% / 0.25)", "tracking-normal": "0em"}, "dark": {"background": "oklch(0.23 0.01 264.29)", "foreground": "oklch(0.92 0 0)", "card": "oklch(0.32 0.01 223.67)", "card-foreground": "oklch(0.92 0 0)", "popover": "oklch(0.32 0.01 223.67)", "popover-foreground": "oklch(0.92 0 0)", "primary": "oklch(0.65 0.15 150.31)", "primary-foreground": "oklch(1.00 0 0)", "secondary": "oklch(0.59 0.10 245.74)", "secondary-foreground": "oklch(0.92 0 0)", "muted": "oklch(0.39 0 0)", "muted-foreground": "oklch(0.72 0 0)", "accent": "oklch(0.67 0.14 261.34)", "accent-foreground": "oklch(0.92 0 0)", "destructive": "oklch(0.64 0.21 25.33)", "destructive-foreground": "oklch(1.00 0 0)", "border": "oklch(0.39 0 0)", "input": "oklch(0.39 0 0)", "ring": "oklch(0.65 0.15 150.31)", "chart-1": "oklch(0.65 0.15 150.31)", "chart-2": "oklch(0.59 0.10 245.74)", "chart-3": "oklch(0.67 0.14 261.34)", "chart-4": "oklch(0.83 0.11 211.96)", "chart-5": "oklch(0.59 0.16 148.24)", "radius": "0.5rem", "sidebar": "oklch(0.23 0.01 264.29)", "sidebar-foreground": "oklch(0.92 0 0)", "sidebar-primary": "oklch(0.65 0.15 150.31)", "sidebar-primary-foreground": "oklch(1.00 0 0)", "sidebar-accent": "oklch(0.67 0.14 261.34)", "sidebar-accent-foreground": "oklch(0.92 0 0)", "sidebar-border": "oklch(0.39 0 0)", "sidebar-ring": "oklch(0.65 0.15 150.31)", "font-sans": "Plus Jakarta Sans, sans-serif", "font-serif": "Source Serif 4, serif", "font-mono": "JetBrains Mono, monospace", "shadow-color": "hsl(0 0% 0%)", "shadow-opacity": "0.1", "shadow-blur": "3px", "shadow-spread": "0px", "shadow-offset-x": "0", "shadow-offset-y": "1px", "letter-spacing": "0em", "spacing": "0.25rem", "shadow-2xs": "0 1px 3px 0px hsl(0 0% 0% / 0.05)", "shadow-xs": "0 1px 3px 0px hsl(0 0% 0% / 0.05)", "shadow-sm": "0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 1px 2px -1px hsl(0 0% 0% / 0.10)", "shadow": "0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 1px 2px -1px hsl(0 0% 0% / 0.10)", "shadow-md": "0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 2px 4px -1px hsl(0 0% 0% / 0.10)", "shadow-lg": "0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 4px 6px -1px hsl(0 0% 0% / 0.10)", "shadow-xl": "0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 8px 10px -1px hsl(0 0% 0% / 0.10)", "shadow-2xl": "0 1px 3px 0px hsl(0 0% 0% / 0.25)"}}}, {"name": "vintage-paper", "type": "registry:style", "title": "Vintage Paper", "description": "A theme based on the Vintage Paper color palette.", "css": {"@layer base": {"body": {"letter-spacing": "var(--tracking-normal)"}}}, "cssVars": {"theme": {"font-sans": "Libre Baskerville, serif", "font-mono": "IBM Plex Mono, monospace", "font-serif": "Lo<PERSON>, serif", "radius": "0.25rem", "tracking-tighter": "calc(var(--tracking-normal) - 0.05em)", "tracking-tight": "calc(var(--tracking-normal) - 0.025em)", "tracking-wide": "calc(var(--tracking-normal) + 0.025em)", "tracking-wider": "calc(var(--tracking-normal) + 0.05em)", "tracking-widest": "calc(var(--tracking-normal) + 0.1em)"}, "light": {"background": "oklch(0.96 0.02 90.24)", "foreground": "oklch(0.38 0.02 64.34)", "card": "oklch(0.99 0.01 87.47)", "card-foreground": "oklch(0.38 0.02 64.34)", "popover": "oklch(0.99 0.01 87.47)", "popover-foreground": "oklch(0.38 0.02 64.34)", "primary": "oklch(0.62 0.08 65.54)", "primary-foreground": "oklch(1.00 0 0)", "secondary": "oklch(0.88 0.03 85.57)", "secondary-foreground": "oklch(0.43 0.03 64.93)", "muted": "oklch(0.92 0.02 83.06)", "muted-foreground": "oklch(0.54 0.04 71.17)", "accent": "oklch(0.83 0.04 88.81)", "accent-foreground": "oklch(0.38 0.02 64.34)", "destructive": "oklch(0.55 0.14 32.91)", "destructive-foreground": "oklch(1.00 0 0)", "border": "oklch(0.86 0.03 84.59)", "input": "oklch(0.86 0.03 84.59)", "ring": "oklch(0.62 0.08 65.54)", "chart-1": "oklch(0.62 0.08 65.54)", "chart-2": "oklch(0.56 0.06 68.58)", "chart-3": "oklch(0.49 0.06 72.68)", "chart-4": "oklch(0.68 0.06 64.78)", "chart-5": "oklch(0.73 0.06 66.70)", "radius": "0.25rem", "sidebar": "oklch(0.92 0.02 83.06)", "sidebar-foreground": "oklch(0.38 0.02 64.34)", "sidebar-primary": "oklch(0.62 0.08 65.54)", "sidebar-primary-foreground": "oklch(1.00 0 0)", "sidebar-accent": "oklch(0.83 0.04 88.81)", "sidebar-accent-foreground": "oklch(0.38 0.02 64.34)", "sidebar-border": "oklch(0.86 0.03 84.59)", "sidebar-ring": "oklch(0.62 0.08 65.54)", "font-sans": "Libre Baskerville, serif", "font-serif": "Lo<PERSON>, serif", "font-mono": "IBM Plex Mono, monospace", "shadow-color": "hsl(28 13% 20%)", "shadow-opacity": "0.12", "shadow-blur": "5px", "shadow-spread": "0px", "shadow-offset-x": "2px", "shadow-offset-y": "3px", "letter-spacing": "0em", "spacing": "0.25rem", "shadow-2xs": "2px 3px 5px 0px hsl(28 13% 20% / 0.06)", "shadow-xs": "2px 3px 5px 0px hsl(28 13% 20% / 0.06)", "shadow-sm": "2px 3px 5px 0px hsl(28 13% 20% / 0.12), 2px 1px 2px -1px hsl(28 13% 20% / 0.12)", "shadow": "2px 3px 5px 0px hsl(28 13% 20% / 0.12), 2px 1px 2px -1px hsl(28 13% 20% / 0.12)", "shadow-md": "2px 3px 5px 0px hsl(28 13% 20% / 0.12), 2px 2px 4px -1px hsl(28 13% 20% / 0.12)", "shadow-lg": "2px 3px 5px 0px hsl(28 13% 20% / 0.12), 2px 4px 6px -1px hsl(28 13% 20% / 0.12)", "shadow-xl": "2px 3px 5px 0px hsl(28 13% 20% / 0.12), 2px 8px 10px -1px hsl(28 13% 20% / 0.12)", "shadow-2xl": "2px 3px 5px 0px hsl(28 13% 20% / 0.30)", "tracking-normal": "0em"}, "dark": {"background": "oklch(0.27 0.01 57.65)", "foreground": "oklch(0.92 0.02 83.06)", "card": "oklch(0.32 0.02 59.06)", "card-foreground": "oklch(0.92 0.02 83.06)", "popover": "oklch(0.32 0.02 59.06)", "popover-foreground": "oklch(0.92 0.02 83.06)", "primary": "oklch(0.73 0.06 66.70)", "primary-foreground": "oklch(0.27 0.01 57.65)", "secondary": "oklch(0.38 0.02 57.13)", "secondary-foreground": "oklch(0.92 0.02 83.06)", "muted": "oklch(0.32 0.02 59.06)", "muted-foreground": "oklch(0.80 0.02 82.11)", "accent": "oklch(0.42 0.03 56.34)", "accent-foreground": "oklch(0.92 0.02 83.06)", "destructive": "oklch(0.55 0.14 32.91)", "destructive-foreground": "oklch(1.00 0 0)", "border": "oklch(0.38 0.02 57.13)", "input": "oklch(0.38 0.02 57.13)", "ring": "oklch(0.73 0.06 66.70)", "chart-1": "oklch(0.73 0.06 66.70)", "chart-2": "oklch(0.68 0.06 64.78)", "chart-3": "oklch(0.62 0.08 65.54)", "chart-4": "oklch(0.56 0.06 68.58)", "chart-5": "oklch(0.49 0.06 72.68)", "radius": "0.25rem", "sidebar": "oklch(0.27 0.01 57.65)", "sidebar-foreground": "oklch(0.92 0.02 83.06)", "sidebar-primary": "oklch(0.73 0.06 66.70)", "sidebar-primary-foreground": "oklch(0.27 0.01 57.65)", "sidebar-accent": "oklch(0.42 0.03 56.34)", "sidebar-accent-foreground": "oklch(0.92 0.02 83.06)", "sidebar-border": "oklch(0.38 0.02 57.13)", "sidebar-ring": "oklch(0.73 0.06 66.70)", "font-sans": "Libre Baskerville, serif", "font-serif": "Lo<PERSON>, serif", "font-mono": "IBM Plex Mono, monospace", "shadow-color": "hsl(28 13% 20%)", "shadow-opacity": "0.12", "shadow-blur": "5px", "shadow-spread": "0px", "shadow-offset-x": "2px", "shadow-offset-y": "3px", "letter-spacing": "0em", "spacing": "0.25rem", "shadow-2xs": "2px 3px 5px 0px hsl(28 13% 20% / 0.06)", "shadow-xs": "2px 3px 5px 0px hsl(28 13% 20% / 0.06)", "shadow-sm": "2px 3px 5px 0px hsl(28 13% 20% / 0.12), 2px 1px 2px -1px hsl(28 13% 20% / 0.12)", "shadow": "2px 3px 5px 0px hsl(28 13% 20% / 0.12), 2px 1px 2px -1px hsl(28 13% 20% / 0.12)", "shadow-md": "2px 3px 5px 0px hsl(28 13% 20% / 0.12), 2px 2px 4px -1px hsl(28 13% 20% / 0.12)", "shadow-lg": "2px 3px 5px 0px hsl(28 13% 20% / 0.12), 2px 4px 6px -1px hsl(28 13% 20% / 0.12)", "shadow-xl": "2px 3px 5px 0px hsl(28 13% 20% / 0.12), 2px 8px 10px -1px hsl(28 13% 20% / 0.12)", "shadow-2xl": "2px 3px 5px 0px hsl(28 13% 20% / 0.30)"}}}, {"name": "sunset-horizon", "type": "registry:style", "title": "Sunset Horizon", "description": "A theme based on the Sunset Horizon color palette.", "css": {"@layer base": {"body": {"letter-spacing": "var(--tracking-normal)"}}}, "cssVars": {"theme": {"font-sans": "Montserrat, sans-serif", "font-mono": "Ubuntu Mono, monospace", "font-serif": "<PERSON><PERSON><PERSON><PERSON>, serif", "radius": "0.625rem", "tracking-tighter": "calc(var(--tracking-normal) - 0.05em)", "tracking-tight": "calc(var(--tracking-normal) - 0.025em)", "tracking-wide": "calc(var(--tracking-normal) + 0.025em)", "tracking-wider": "calc(var(--tracking-normal) + 0.05em)", "tracking-widest": "calc(var(--tracking-normal) + 0.1em)"}, "light": {"background": "oklch(0.99 0.01 56.32)", "foreground": "oklch(0.34 0.01 2.77)", "card": "oklch(1.00 0 0)", "card-foreground": "oklch(0.34 0.01 2.77)", "popover": "oklch(1.00 0 0)", "popover-foreground": "oklch(0.34 0.01 2.77)", "primary": "oklch(0.74 0.16 34.71)", "primary-foreground": "oklch(1.00 0 0)", "secondary": "oklch(0.96 0.02 28.90)", "secondary-foreground": "oklch(0.56 0.13 32.74)", "muted": "oklch(0.97 0.02 39.40)", "muted-foreground": "oklch(0.55 0.01 58.07)", "accent": "oklch(0.83 0.11 58.00)", "accent-foreground": "oklch(0.34 0.01 2.77)", "destructive": "oklch(0.61 0.21 22.24)", "destructive-foreground": "oklch(1.00 0 0)", "border": "oklch(0.93 0.04 38.69)", "input": "oklch(0.93 0.04 38.69)", "ring": "oklch(0.74 0.16 34.71)", "chart-1": "oklch(0.74 0.16 34.71)", "chart-2": "oklch(0.83 0.11 58.00)", "chart-3": "oklch(0.88 0.08 54.93)", "chart-4": "oklch(0.82 0.11 40.89)", "chart-5": "oklch(0.64 0.13 32.07)", "radius": "0.625rem", "sidebar": "oklch(0.97 0.02 39.40)", "sidebar-foreground": "oklch(0.34 0.01 2.77)", "sidebar-primary": "oklch(0.74 0.16 34.71)", "sidebar-primary-foreground": "oklch(1.00 0 0)", "sidebar-accent": "oklch(0.83 0.11 58.00)", "sidebar-accent-foreground": "oklch(0.34 0.01 2.77)", "sidebar-border": "oklch(0.93 0.04 38.69)", "sidebar-ring": "oklch(0.74 0.16 34.71)", "font-sans": "Montserrat, sans-serif", "font-serif": "<PERSON><PERSON><PERSON><PERSON>, serif", "font-mono": "Ubuntu Mono, monospace", "shadow-color": "hsl(0 0% 0%)", "shadow-opacity": "0.09", "shadow-blur": "12px", "shadow-spread": "-3px", "shadow-offset-x": "0px", "shadow-offset-y": "6px", "letter-spacing": "0em", "spacing": "0.25rem", "shadow-2xs": "0px 6px 12px -3px hsl(0 0% 0% / 0.04)", "shadow-xs": "0px 6px 12px -3px hsl(0 0% 0% / 0.04)", "shadow-sm": "0px 6px 12px -3px hsl(0 0% 0% / 0.09), 0px 1px 2px -4px hsl(0 0% 0% / 0.09)", "shadow": "0px 6px 12px -3px hsl(0 0% 0% / 0.09), 0px 1px 2px -4px hsl(0 0% 0% / 0.09)", "shadow-md": "0px 6px 12px -3px hsl(0 0% 0% / 0.09), 0px 2px 4px -4px hsl(0 0% 0% / 0.09)", "shadow-lg": "0px 6px 12px -3px hsl(0 0% 0% / 0.09), 0px 4px 6px -4px hsl(0 0% 0% / 0.09)", "shadow-xl": "0px 6px 12px -3px hsl(0 0% 0% / 0.09), 0px 8px 10px -4px hsl(0 0% 0% / 0.09)", "shadow-2xl": "0px 6px 12px -3px hsl(0 0% 0% / 0.22)", "tracking-normal": "0em"}, "dark": {"background": "oklch(0.26 0.02 352.40)", "foreground": "oklch(0.94 0.01 51.32)", "card": "oklch(0.32 0.02 341.45)", "card-foreground": "oklch(0.94 0.01 51.32)", "popover": "oklch(0.32 0.02 341.45)", "popover-foreground": "oklch(0.94 0.01 51.32)", "primary": "oklch(0.74 0.16 34.71)", "primary-foreground": "oklch(1.00 0 0)", "secondary": "oklch(0.36 0.02 342.27)", "secondary-foreground": "oklch(0.94 0.01 51.32)", "muted": "oklch(0.32 0.02 341.45)", "muted-foreground": "oklch(0.84 0.02 52.63)", "accent": "oklch(0.83 0.11 58.00)", "accent-foreground": "oklch(0.26 0.02 352.40)", "destructive": "oklch(0.61 0.21 22.24)", "destructive-foreground": "oklch(1.00 0 0)", "border": "oklch(0.36 0.02 342.27)", "input": "oklch(0.36 0.02 342.27)", "ring": "oklch(0.74 0.16 34.71)", "chart-1": "oklch(0.74 0.16 34.71)", "chart-2": "oklch(0.83 0.11 58.00)", "chart-3": "oklch(0.88 0.08 54.93)", "chart-4": "oklch(0.82 0.11 40.89)", "chart-5": "oklch(0.64 0.13 32.07)", "radius": "0.625rem", "sidebar": "oklch(0.26 0.02 352.40)", "sidebar-foreground": "oklch(0.94 0.01 51.32)", "sidebar-primary": "oklch(0.74 0.16 34.71)", "sidebar-primary-foreground": "oklch(1.00 0 0)", "sidebar-accent": "oklch(0.83 0.11 58.00)", "sidebar-accent-foreground": "oklch(0.26 0.02 352.40)", "sidebar-border": "oklch(0.36 0.02 342.27)", "sidebar-ring": "oklch(0.74 0.16 34.71)", "font-sans": "Montserrat, sans-serif", "font-serif": "<PERSON><PERSON><PERSON><PERSON>, serif", "font-mono": "Ubuntu Mono, monospace", "shadow-color": "hsl(0 0% 0%)", "shadow-opacity": "0.09", "shadow-blur": "12px", "shadow-spread": "-3px", "shadow-offset-x": "0px", "shadow-offset-y": "6px", "letter-spacing": "0em", "spacing": "0.25rem", "shadow-2xs": "0px 6px 12px -3px hsl(0 0% 0% / 0.04)", "shadow-xs": "0px 6px 12px -3px hsl(0 0% 0% / 0.04)", "shadow-sm": "0px 6px 12px -3px hsl(0 0% 0% / 0.09), 0px 1px 2px -4px hsl(0 0% 0% / 0.09)", "shadow": "0px 6px 12px -3px hsl(0 0% 0% / 0.09), 0px 1px 2px -4px hsl(0 0% 0% / 0.09)", "shadow-md": "0px 6px 12px -3px hsl(0 0% 0% / 0.09), 0px 2px 4px -4px hsl(0 0% 0% / 0.09)", "shadow-lg": "0px 6px 12px -3px hsl(0 0% 0% / 0.09), 0px 4px 6px -4px hsl(0 0% 0% / 0.09)", "shadow-xl": "0px 6px 12px -3px hsl(0 0% 0% / 0.09), 0px 8px 10px -4px hsl(0 0% 0% / 0.09)", "shadow-2xl": "0px 6px 12px -3px hsl(0 0% 0% / 0.22)"}}}, {"name": "starry-night", "type": "registry:style", "title": "Starry Night", "description": "A theme based on the Starry Night color palette.", "css": {"@layer base": {"body": {"letter-spacing": "var(--tracking-normal)"}}}, "cssVars": {"theme": {"font-sans": "Libre Baskerville, serif", "font-mono": "ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, \"Liberation Mono\", \"Courier New\", monospace", "font-serif": "ui-serif, Georgia, Cambria, \"Times New Roman\", Times, serif", "radius": "0.5rem", "tracking-tighter": "calc(var(--tracking-normal) - 0.05em)", "tracking-tight": "calc(var(--tracking-normal) - 0.025em)", "tracking-wide": "calc(var(--tracking-normal) + 0.025em)", "tracking-wider": "calc(var(--tracking-normal) + 0.05em)", "tracking-widest": "calc(var(--tracking-normal) + 0.1em)"}, "light": {"background": "oklch(0.98 0.00 258.32)", "foreground": "oklch(0.26 0.04 268.07)", "card": "oklch(0.93 0.01 251.56)", "card-foreground": "oklch(0.26 0.04 268.07)", "popover": "oklch(0.99 0.03 98.05)", "popover-foreground": "oklch(0.26 0.04 268.07)", "primary": "oklch(0.48 0.12 263.38)", "primary-foreground": "oklch(0.99 0.03 98.05)", "secondary": "oklch(0.86 0.12 81.01)", "secondary-foreground": "oklch(0.26 0.04 268.07)", "muted": "oklch(0.92 0.01 106.56)", "muted-foreground": "oklch(0.48 0.12 263.38)", "accent": "oklch(0.69 0.07 234.04)", "accent-foreground": "oklch(0.99 0.03 98.05)", "destructive": "oklch(0.26 0.04 322.53)", "destructive-foreground": "oklch(0.99 0.03 98.05)", "border": "oklch(0.78 0.02 251.19)", "input": "oklch(0.69 0.07 234.04)", "ring": "oklch(0.86 0.12 81.01)", "chart-1": "oklch(0.48 0.12 263.38)", "chart-2": "oklch(0.86 0.12 81.01)", "chart-3": "oklch(0.69 0.07 234.04)", "chart-4": "oklch(0.78 0.02 251.19)", "chart-5": "oklch(0.26 0.04 322.53)", "radius": "0.5rem", "sidebar": "oklch(0.93 0.01 251.56)", "sidebar-foreground": "oklch(0.26 0.04 268.07)", "sidebar-primary": "oklch(0.48 0.12 263.38)", "sidebar-primary-foreground": "oklch(0.99 0.03 98.05)", "sidebar-accent": "oklch(0.86 0.12 81.01)", "sidebar-accent-foreground": "oklch(0.26 0.04 268.07)", "sidebar-border": "oklch(0.78 0.02 251.19)", "sidebar-ring": "oklch(0.86 0.12 81.01)", "font-sans": "Libre Baskerville, serif", "font-serif": "ui-serif, Georgia, Cambria, \"Times New Roman\", Times, serif", "font-mono": "ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, \"Liberation Mono\", \"Courier New\", monospace", "shadow-color": "hsl(0 0% 0%)", "shadow-opacity": "0.1", "shadow-blur": "3px", "shadow-spread": "0px", "shadow-offset-x": "0", "shadow-offset-y": "1px", "letter-spacing": "0em", "spacing": "0.25rem", "shadow-2xs": "0 1px 3px 0px hsl(0 0% 0% / 0.05)", "shadow-xs": "0 1px 3px 0px hsl(0 0% 0% / 0.05)", "shadow-sm": "0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 1px 2px -1px hsl(0 0% 0% / 0.10)", "shadow": "0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 1px 2px -1px hsl(0 0% 0% / 0.10)", "shadow-md": "0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 2px 4px -1px hsl(0 0% 0% / 0.10)", "shadow-lg": "0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 4px 6px -1px hsl(0 0% 0% / 0.10)", "shadow-xl": "0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 8px 10px -1px hsl(0 0% 0% / 0.10)", "shadow-2xl": "0 1px 3px 0px hsl(0 0% 0% / 0.25)", "tracking-normal": "0em"}, "dark": {"background": "oklch(0.22 0.02 275.84)", "foreground": "oklch(0.94 0.01 266.70)", "card": "oklch(0.27 0.04 281.30)", "card-foreground": "oklch(0.94 0.01 266.70)", "popover": "oklch(0.27 0.04 281.30)", "popover-foreground": "oklch(0.91 0.14 95.11)", "primary": "oklch(0.48 0.12 263.38)", "primary-foreground": "oklch(0.91 0.14 95.11)", "secondary": "oklch(0.91 0.14 95.11)", "secondary-foreground": "oklch(0.27 0.04 281.30)", "muted": "oklch(0.27 0.04 281.30)", "muted-foreground": "oklch(0.62 0.04 262.04)", "accent": "oklch(0.85 0.05 264.78)", "accent-foreground": "oklch(0.22 0.02 275.84)", "destructive": "oklch(0.53 0.12 357.11)", "destructive-foreground": "oklch(0.91 0.14 95.11)", "border": "oklch(0.31 0.03 281.77)", "input": "oklch(0.48 0.12 263.38)", "ring": "oklch(0.91 0.14 95.11)", "chart-1": "oklch(0.48 0.12 263.38)", "chart-2": "oklch(0.91 0.14 95.11)", "chart-3": "oklch(0.69 0.07 234.04)", "chart-4": "oklch(0.62 0.04 262.04)", "chart-5": "oklch(0.53 0.12 357.11)", "radius": "0.5rem", "sidebar": "oklch(0.27 0.04 281.30)", "sidebar-foreground": "oklch(0.94 0.01 266.70)", "sidebar-primary": "oklch(0.48 0.12 263.38)", "sidebar-primary-foreground": "oklch(0.91 0.14 95.11)", "sidebar-accent": "oklch(0.91 0.14 95.11)", "sidebar-accent-foreground": "oklch(0.27 0.04 281.30)", "sidebar-border": "oklch(0.31 0.03 281.77)", "sidebar-ring": "oklch(0.91 0.14 95.11)", "font-sans": "Libre Baskerville, serif", "font-serif": "ui-serif, Georgia, Cambria, \"Times New Roman\", Times, serif", "font-mono": "ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, \"Liberation Mono\", \"Courier New\", monospace", "shadow-color": "hsl(0 0% 0%)", "shadow-opacity": "0.1", "shadow-blur": "3px", "shadow-spread": "0px", "shadow-offset-x": "0", "shadow-offset-y": "1px", "letter-spacing": "0em", "spacing": "0.25rem", "shadow-2xs": "0 1px 3px 0px hsl(0 0% 0% / 0.05)", "shadow-xs": "0 1px 3px 0px hsl(0 0% 0% / 0.05)", "shadow-sm": "0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 1px 2px -1px hsl(0 0% 0% / 0.10)", "shadow": "0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 1px 2px -1px hsl(0 0% 0% / 0.10)", "shadow-md": "0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 2px 4px -1px hsl(0 0% 0% / 0.10)", "shadow-lg": "0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 4px 6px -1px hsl(0 0% 0% / 0.10)", "shadow-xl": "0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 8px 10px -1px hsl(0 0% 0% / 0.10)", "shadow-2xl": "0 1px 3px 0px hsl(0 0% 0% / 0.25)"}}}, {"name": "claude", "type": "registry:style", "title": "<PERSON>", "description": "A theme based on the <PERSON> color palette.", "css": {"@layer base": {"body": {"letter-spacing": "var(--tracking-normal)"}}}, "cssVars": {"theme": {"font-sans": "ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', <PERSON>l, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji'", "font-mono": "ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, \"Liberation Mono\", \"Courier New\", monospace", "font-serif": "ui-serif, Georgia, Cambria, \"Times New Roman\", Times, serif", "radius": "0.5rem", "tracking-tighter": "calc(var(--tracking-normal) - 0.05em)", "tracking-tight": "calc(var(--tracking-normal) - 0.025em)", "tracking-wide": "calc(var(--tracking-normal) + 0.025em)", "tracking-wider": "calc(var(--tracking-normal) + 0.05em)", "tracking-widest": "calc(var(--tracking-normal) + 0.1em)"}, "light": {"background": "oklch(0.98 0.01 95.10)", "foreground": "oklch(0.34 0.03 95.72)", "card": "oklch(0.98 0.01 95.10)", "card-foreground": "oklch(0.19 0.00 106.59)", "popover": "oklch(1.00 0 0)", "popover-foreground": "oklch(0.27 0.02 98.94)", "primary": "oklch(0.62 0.14 39.04)", "primary-foreground": "oklch(1.00 0 0)", "secondary": "oklch(0.92 0.01 92.99)", "secondary-foreground": "oklch(0.43 0.02 98.60)", "muted": "oklch(0.93 0.02 90.24)", "muted-foreground": "oklch(0.61 0.01 97.42)", "accent": "oklch(0.92 0.01 92.99)", "accent-foreground": "oklch(0.27 0.02 98.94)", "destructive": "oklch(0.19 0.00 106.59)", "destructive-foreground": "oklch(1.00 0 0)", "border": "oklch(0.88 0.01 97.36)", "input": "oklch(0.76 0.02 98.35)", "ring": "oklch(0.59 0.17 253.06)", "chart-1": "oklch(0.56 0.13 43.00)", "chart-2": "oklch(0.69 0.16 290.41)", "chart-3": "oklch(0.88 0.03 93.13)", "chart-4": "oklch(0.88 0.04 298.18)", "chart-5": "oklch(0.56 0.13 42.06)", "radius": "0.5rem", "sidebar": "oklch(0.97 0.01 98.88)", "sidebar-foreground": "oklch(0.36 0.01 106.65)", "sidebar-primary": "oklch(0.62 0.14 39.04)", "sidebar-primary-foreground": "oklch(0.99 0 0)", "sidebar-accent": "oklch(0.92 0.01 92.99)", "sidebar-accent-foreground": "oklch(0.33 0 0)", "sidebar-border": "oklch(0.94 0 0)", "sidebar-ring": "oklch(0.77 0 0)", "font-sans": "ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', <PERSON>l, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji'", "font-serif": "ui-serif, Georgia, Cambria, \"Times New Roman\", Times, serif", "font-mono": "ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, \"Liberation Mono\", \"Courier New\", monospace", "shadow-color": "hsl(0 0% 0%)", "shadow-opacity": "0.1", "shadow-blur": "3px", "shadow-spread": "0px", "shadow-offset-x": "0", "shadow-offset-y": "1px", "letter-spacing": "0em", "spacing": "0.25rem", "shadow-2xs": "0 1px 3px 0px hsl(0 0% 0% / 0.05)", "shadow-xs": "0 1px 3px 0px hsl(0 0% 0% / 0.05)", "shadow-sm": "0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 1px 2px -1px hsl(0 0% 0% / 0.10)", "shadow": "0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 1px 2px -1px hsl(0 0% 0% / 0.10)", "shadow-md": "0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 2px 4px -1px hsl(0 0% 0% / 0.10)", "shadow-lg": "0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 4px 6px -1px hsl(0 0% 0% / 0.10)", "shadow-xl": "0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 8px 10px -1px hsl(0 0% 0% / 0.10)", "shadow-2xl": "0 1px 3px 0px hsl(0 0% 0% / 0.25)", "tracking-normal": "0em"}, "dark": {"background": "oklch(0.27 0.00 106.64)", "foreground": "oklch(0.81 0.01 93.01)", "card": "oklch(0.27 0.00 106.64)", "card-foreground": "oklch(0.98 0.01 95.10)", "popover": "oklch(0.31 0.00 106.60)", "popover-foreground": "oklch(0.92 0.00 106.48)", "primary": "oklch(0.67 0.13 38.76)", "primary-foreground": "oklch(1.00 0 0)", "secondary": "oklch(0.98 0.01 95.10)", "secondary-foreground": "oklch(0.31 0.00 106.60)", "muted": "oklch(0.22 0.00 106.71)", "muted-foreground": "oklch(0.77 0.02 99.07)", "accent": "oklch(0.21 0.01 95.42)", "accent-foreground": "oklch(0.97 0.01 98.88)", "destructive": "oklch(0.64 0.21 25.33)", "destructive-foreground": "oklch(1.00 0 0)", "border": "oklch(0.36 0.01 106.89)", "input": "oklch(0.43 0.01 100.22)", "ring": "oklch(0.59 0.17 253.06)", "chart-1": "oklch(0.56 0.13 43.00)", "chart-2": "oklch(0.69 0.16 290.41)", "chart-3": "oklch(0.21 0.01 95.42)", "chart-4": "oklch(0.31 0.05 289.32)", "chart-5": "oklch(0.56 0.13 42.06)", "radius": "0.5rem", "sidebar": "oklch(0.24 0.00 67.71)", "sidebar-foreground": "oklch(0.81 0.01 93.01)", "sidebar-primary": "oklch(0.33 0 0)", "sidebar-primary-foreground": "oklch(0.99 0 0)", "sidebar-accent": "oklch(0.17 0.00 106.62)", "sidebar-accent-foreground": "oklch(0.81 0.01 93.01)", "sidebar-border": "oklch(0.94 0 0)", "sidebar-ring": "oklch(0.77 0 0)", "font-sans": "ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', <PERSON>l, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji'", "font-serif": "ui-serif, Georgia, Cambria, \"Times New Roman\", Times, serif", "font-mono": "ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, \"Liberation Mono\", \"Courier New\", monospace", "shadow-color": "hsl(0 0% 0%)", "shadow-opacity": "0.1", "shadow-blur": "3px", "shadow-spread": "0px", "shadow-offset-x": "0", "shadow-offset-y": "1px", "letter-spacing": "0em", "spacing": "0.25rem", "shadow-2xs": "0 1px 3px 0px hsl(0 0% 0% / 0.05)", "shadow-xs": "0 1px 3px 0px hsl(0 0% 0% / 0.05)", "shadow-sm": "0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 1px 2px -1px hsl(0 0% 0% / 0.10)", "shadow": "0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 1px 2px -1px hsl(0 0% 0% / 0.10)", "shadow-md": "0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 2px 4px -1px hsl(0 0% 0% / 0.10)", "shadow-lg": "0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 4px 6px -1px hsl(0 0% 0% / 0.10)", "shadow-xl": "0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 8px 10px -1px hsl(0 0% 0% / 0.10)", "shadow-2xl": "0 1px 3px 0px hsl(0 0% 0% / 0.25)"}}}, {"name": "vercel", "type": "registry:style", "title": "Vercel", "description": "A theme based on the Vercel color palette.", "css": {"@layer base": {"body": {"letter-spacing": "var(--tracking-normal)"}}}, "cssVars": {"theme": {"font-sans": "<PERSON><PERSON><PERSON>, sans-serif", "font-mono": "Geist Mono, monospace", "font-serif": "Georgia, serif", "radius": "0.5rem", "tracking-tighter": "calc(var(--tracking-normal) - 0.05em)", "tracking-tight": "calc(var(--tracking-normal) - 0.025em)", "tracking-wide": "calc(var(--tracking-normal) + 0.025em)", "tracking-wider": "calc(var(--tracking-normal) + 0.05em)", "tracking-widest": "calc(var(--tracking-normal) + 0.1em)"}, "light": {"background": "oklch(0.99 0 0)", "foreground": "oklch(0 0 0)", "card": "oklch(1 0 0)", "card-foreground": "oklch(0 0 0)", "popover": "oklch(0.99 0 0)", "popover-foreground": "oklch(0 0 0)", "primary": "oklch(0 0 0)", "primary-foreground": "oklch(1 0 0)", "secondary": "oklch(0.94 0 0)", "secondary-foreground": "oklch(0 0 0)", "muted": "oklch(0.97 0 0)", "muted-foreground": "oklch(0.44 0 0)", "accent": "oklch(0.94 0 0)", "accent-foreground": "oklch(0 0 0)", "destructive": "oklch(0.63 0.19 23.03)", "destructive-foreground": "oklch(1 0 0)", "border": "oklch(0.92 0 0)", "input": "oklch(0.94 0 0)", "ring": "oklch(0 0 0)", "chart-1": "oklch(0.81 0.17 75.35)", "chart-2": "oklch(0.55 0.22 264.53)", "chart-3": "oklch(0.72 0 0)", "chart-4": "oklch(0.92 0 0)", "chart-5": "oklch(0.56 0 0)", "radius": "0.5rem", "sidebar": "oklch(0.99 0 0)", "sidebar-foreground": "oklch(0 0 0)", "sidebar-primary": "oklch(0 0 0)", "sidebar-primary-foreground": "oklch(1 0 0)", "sidebar-accent": "oklch(0.94 0 0)", "sidebar-accent-foreground": "oklch(0 0 0)", "sidebar-border": "oklch(0.94 0 0)", "sidebar-ring": "oklch(0 0 0)", "font-sans": "<PERSON><PERSON><PERSON>, sans-serif", "font-serif": "Georgia, serif", "font-mono": "Geist Mono, monospace", "shadow-color": "hsl(0 0% 0%)", "shadow-opacity": "0.18", "shadow-blur": "2px", "shadow-spread": "0px", "shadow-offset-x": "0px", "shadow-offset-y": "1px", "letter-spacing": "0em", "spacing": "0.25rem", "shadow-2xs": "0px 1px 2px 0px hsl(0 0% 0% / 0.09)", "shadow-xs": "0px 1px 2px 0px hsl(0 0% 0% / 0.09)", "shadow-sm": "0px 1px 2px 0px hsl(0 0% 0% / 0.18), 0px 1px 2px -1px hsl(0 0% 0% / 0.18)", "shadow": "0px 1px 2px 0px hsl(0 0% 0% / 0.18), 0px 1px 2px -1px hsl(0 0% 0% / 0.18)", "shadow-md": "0px 1px 2px 0px hsl(0 0% 0% / 0.18), 0px 2px 4px -1px hsl(0 0% 0% / 0.18)", "shadow-lg": "0px 1px 2px 0px hsl(0 0% 0% / 0.18), 0px 4px 6px -1px hsl(0 0% 0% / 0.18)", "shadow-xl": "0px 1px 2px 0px hsl(0 0% 0% / 0.18), 0px 8px 10px -1px hsl(0 0% 0% / 0.18)", "shadow-2xl": "0px 1px 2px 0px hsl(0 0% 0% / 0.45)", "tracking-normal": "0em"}, "dark": {"background": "oklch(0 0 0)", "foreground": "oklch(1 0 0)", "card": "oklch(0.14 0 0)", "card-foreground": "oklch(1 0 0)", "popover": "oklch(0.18 0 0)", "popover-foreground": "oklch(1 0 0)", "primary": "oklch(1 0 0)", "primary-foreground": "oklch(0 0 0)", "secondary": "oklch(0.25 0 0)", "secondary-foreground": "oklch(1 0 0)", "muted": "oklch(0.23 0 0)", "muted-foreground": "oklch(0.72 0 0)", "accent": "oklch(0.32 0 0)", "accent-foreground": "oklch(1 0 0)", "destructive": "oklch(0.69 0.20 23.91)", "destructive-foreground": "oklch(0 0 0)", "border": "oklch(0.26 0 0)", "input": "oklch(0.32 0 0)", "ring": "oklch(0.72 0 0)", "chart-1": "oklch(0.81 0.17 75.35)", "chart-2": "oklch(0.58 0.21 260.84)", "chart-3": "oklch(0.56 0 0)", "chart-4": "oklch(0.44 0 0)", "chart-5": "oklch(0.92 0 0)", "radius": "0.5rem", "sidebar": "oklch(0.18 0 0)", "sidebar-foreground": "oklch(1 0 0)", "sidebar-primary": "oklch(1 0 0)", "sidebar-primary-foreground": "oklch(0 0 0)", "sidebar-accent": "oklch(0.32 0 0)", "sidebar-accent-foreground": "oklch(1 0 0)", "sidebar-border": "oklch(0.32 0 0)", "sidebar-ring": "oklch(0.72 0 0)", "font-sans": "<PERSON><PERSON><PERSON>, sans-serif", "font-serif": "Georgia, serif", "font-mono": "Geist Mono, monospace", "shadow-color": "hsl(0 0% 0%)", "shadow-opacity": "0.18", "shadow-blur": "2px", "shadow-spread": "0px", "shadow-offset-x": "0px", "shadow-offset-y": "1px", "letter-spacing": "0em", "spacing": "0.25rem", "shadow-2xs": "0px 1px 2px 0px hsl(0 0% 0% / 0.09)", "shadow-xs": "0px 1px 2px 0px hsl(0 0% 0% / 0.09)", "shadow-sm": "0px 1px 2px 0px hsl(0 0% 0% / 0.18), 0px 1px 2px -1px hsl(0 0% 0% / 0.18)", "shadow": "0px 1px 2px 0px hsl(0 0% 0% / 0.18), 0px 1px 2px -1px hsl(0 0% 0% / 0.18)", "shadow-md": "0px 1px 2px 0px hsl(0 0% 0% / 0.18), 0px 2px 4px -1px hsl(0 0% 0% / 0.18)", "shadow-lg": "0px 1px 2px 0px hsl(0 0% 0% / 0.18), 0px 4px 6px -1px hsl(0 0% 0% / 0.18)", "shadow-xl": "0px 1px 2px 0px hsl(0 0% 0% / 0.18), 0px 8px 10px -1px hsl(0 0% 0% / 0.18)", "shadow-2xl": "0px 1px 2px 0px hsl(0 0% 0% / 0.45)"}}}, {"name": "mono", "type": "registry:style", "title": "Mono", "description": "A theme based on the Mono color palette.", "css": {"@layer base": {"body": {"letter-spacing": "var(--tracking-normal)"}}}, "cssVars": {"theme": {"font-sans": "Geist Mono, monospace", "font-mono": "Geist Mono, monospace", "font-serif": "Geist Mono, monospace", "radius": "0rem", "tracking-tighter": "calc(var(--tracking-normal) - 0.05em)", "tracking-tight": "calc(var(--tracking-normal) - 0.025em)", "tracking-wide": "calc(var(--tracking-normal) + 0.025em)", "tracking-wider": "calc(var(--tracking-normal) + 0.05em)", "tracking-widest": "calc(var(--tracking-normal) + 0.1em)"}, "light": {"background": "oklch(1.00 0 0)", "foreground": "oklch(0.14 0 0)", "card": "oklch(1.00 0 0)", "card-foreground": "oklch(0.14 0 0)", "popover": "oklch(1.00 0 0)", "popover-foreground": "oklch(0.14 0 0)", "primary": "oklch(0.56 0 0)", "primary-foreground": "oklch(0.99 0 0)", "secondary": "oklch(0.97 0 0)", "secondary-foreground": "oklch(0.20 0 0)", "muted": "oklch(0.97 0 0)", "muted-foreground": "oklch(0.55 0 0)", "accent": "oklch(0.97 0 0)", "accent-foreground": "oklch(0.20 0 0)", "destructive": "oklch(0.58 0.24 28.48)", "destructive-foreground": "oklch(0.97 0 0)", "border": "oklch(0.92 0 0)", "input": "oklch(0.92 0 0)", "ring": "oklch(0.71 0 0)", "chart-1": "oklch(0.56 0 0)", "chart-2": "oklch(0.56 0 0)", "chart-3": "oklch(0.56 0 0)", "chart-4": "oklch(0.56 0 0)", "chart-5": "oklch(0.56 0 0)", "radius": "0rem", "sidebar": "oklch(0.99 0 0)", "sidebar-foreground": "oklch(0.14 0 0)", "sidebar-primary": "oklch(0.20 0 0)", "sidebar-primary-foreground": "oklch(0.99 0 0)", "sidebar-accent": "oklch(0.97 0 0)", "sidebar-accent-foreground": "oklch(0.20 0 0)", "sidebar-border": "oklch(0.92 0 0)", "sidebar-ring": "oklch(0.71 0 0)", "font-sans": "Geist Mono, monospace", "font-serif": "Geist Mono, monospace", "font-mono": "Geist Mono, monospace", "shadow-color": "hsl(0 0% 0%)", "shadow-opacity": "0", "shadow-blur": "0px", "shadow-spread": "0px", "shadow-offset-x": "0px", "shadow-offset-y": "1px", "letter-spacing": "0em", "spacing": "0.25rem", "shadow-2xs": "0px 1px 0px 0px hsl(0 0% 0% / 0.00)", "shadow-xs": "0px 1px 0px 0px hsl(0 0% 0% / 0.00)", "shadow-sm": "0px 1px 0px 0px hsl(0 0% 0% / 0.00), 0px 1px 2px -1px hsl(0 0% 0% / 0.00)", "shadow": "0px 1px 0px 0px hsl(0 0% 0% / 0.00), 0px 1px 2px -1px hsl(0 0% 0% / 0.00)", "shadow-md": "0px 1px 0px 0px hsl(0 0% 0% / 0.00), 0px 2px 4px -1px hsl(0 0% 0% / 0.00)", "shadow-lg": "0px 1px 0px 0px hsl(0 0% 0% / 0.00), 0px 4px 6px -1px hsl(0 0% 0% / 0.00)", "shadow-xl": "0px 1px 0px 0px hsl(0 0% 0% / 0.00), 0px 8px 10px -1px hsl(0 0% 0% / 0.00)", "shadow-2xl": "0px 1px 0px 0px hsl(0 0% 0% / 0.00)", "tracking-normal": "0em"}, "dark": {"background": "oklch(0.14 0 0)", "foreground": "oklch(0.99 0 0)", "card": "oklch(0.21 0 0)", "card-foreground": "oklch(0.99 0 0)", "popover": "oklch(0.27 0 0)", "popover-foreground": "oklch(0.99 0 0)", "primary": "oklch(0.56 0 0)", "primary-foreground": "oklch(0.99 0 0)", "secondary": "oklch(0.27 0 0)", "secondary-foreground": "oklch(0.99 0 0)", "muted": "oklch(0.27 0 0)", "muted-foreground": "oklch(0.71 0 0)", "accent": "oklch(0.37 0 0)", "accent-foreground": "oklch(0.99 0 0)", "destructive": "oklch(0.70 0.19 22.23)", "destructive-foreground": "oklch(0.27 0 0)", "border": "oklch(0.34 0 0)", "input": "oklch(0.44 0 0)", "ring": "oklch(0.56 0 0)", "chart-1": "oklch(0.56 0 0)", "chart-2": "oklch(0.56 0 0)", "chart-3": "oklch(0.56 0 0)", "chart-4": "oklch(0.56 0 0)", "chart-5": "oklch(0.56 0 0)", "radius": "0rem", "sidebar": "oklch(0.20 0 0)", "sidebar-foreground": "oklch(0.99 0 0)", "sidebar-primary": "oklch(0.99 0 0)", "sidebar-primary-foreground": "oklch(0.20 0 0)", "sidebar-accent": "oklch(0.27 0 0)", "sidebar-accent-foreground": "oklch(0.99 0 0)", "sidebar-border": "oklch(1.00 0 0)", "sidebar-ring": "oklch(0.44 0 0)", "font-sans": "Geist Mono, monospace", "font-serif": "Geist Mono, monospace", "font-mono": "Geist Mono, monospace", "shadow-color": "hsl(0 0% 0%)", "shadow-opacity": "0", "shadow-blur": "0px", "shadow-spread": "0px", "shadow-offset-x": "0px", "shadow-offset-y": "1px", "letter-spacing": "0em", "spacing": "0.25rem", "shadow-2xs": "0px 1px 0px 0px hsl(0 0% 0% / 0.00)", "shadow-xs": "0px 1px 0px 0px hsl(0 0% 0% / 0.00)", "shadow-sm": "0px 1px 0px 0px hsl(0 0% 0% / 0.00), 0px 1px 2px -1px hsl(0 0% 0% / 0.00)", "shadow": "0px 1px 0px 0px hsl(0 0% 0% / 0.00), 0px 1px 2px -1px hsl(0 0% 0% / 0.00)", "shadow-md": "0px 1px 0px 0px hsl(0 0% 0% / 0.00), 0px 2px 4px -1px hsl(0 0% 0% / 0.00)", "shadow-lg": "0px 1px 0px 0px hsl(0 0% 0% / 0.00), 0px 4px 6px -1px hsl(0 0% 0% / 0.00)", "shadow-xl": "0px 1px 0px 0px hsl(0 0% 0% / 0.00), 0px 8px 10px -1px hsl(0 0% 0% / 0.00)", "shadow-2xl": "0px 1px 0px 0px hsl(0 0% 0% / 0.00)"}}}]}