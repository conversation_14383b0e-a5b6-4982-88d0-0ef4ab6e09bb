{"name": "tweakcn-next", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "npm run generate-theme-registry && next build --turbopack", "start": "next start", "lint": "next lint", "generate-theme-registry": "tsx scripts/generate-theme-registry.ts && tsx scripts/generate-registry.ts", "prepare": "husky"}, "dependencies": {"@ai-sdk/google": "^1.2.14", "@base-ui-components/react": "1.0.0-beta.2", "@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@google/generative-ai": "^0.24.1", "@hookform/resolvers": "^5.0.1", "@lexical/react": "^0.31.0", "@neondatabase/serverless": "^1.0.0", "@polar-sh/checkout": "^0.1.11", "@polar-sh/nextjs": "^0.4.3", "@polar-sh/sdk": "^0.34.5", "@radix-ui/react-accordion": "^1.2.7", "@radix-ui/react-alert-dialog": "^1.1.10", "@radix-ui/react-aspect-ratio": "^1.1.4", "@radix-ui/react-avatar": "^1.1.6", "@radix-ui/react-checkbox": "^1.2.2", "@radix-ui/react-collapsible": "^1.1.7", "@radix-ui/react-context-menu": "^2.2.10", "@radix-ui/react-dialog": "^1.1.10", "@radix-ui/react-dropdown-menu": "^2.1.10", "@radix-ui/react-hover-card": "^1.1.10", "@radix-ui/react-label": "^2.1.4", "@radix-ui/react-menubar": "^1.1.10", "@radix-ui/react-navigation-menu": "^1.2.9", "@radix-ui/react-popover": "^1.1.10", "@radix-ui/react-progress": "^1.1.4", "@radix-ui/react-radio-group": "^1.3.2", "@radix-ui/react-scroll-area": "^1.2.5", "@radix-ui/react-select": "^2.2.2", "@radix-ui/react-separator": "^1.1.4", "@radix-ui/react-slider": "^1.3.2", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-switch": "^1.2.2", "@radix-ui/react-tabs": "^1.1.7", "@radix-ui/react-toast": "^1.2.10", "@radix-ui/react-toggle": "^1.1.6", "@radix-ui/react-toggle-group": "^1.1.6", "@radix-ui/react-tooltip": "^1.2.3", "@tanstack/react-query": "^5.81.2", "@tanstack/react-query-devtools": "^5.81.2", "@tanstack/react-table": "^8.21.3", "@tiptap/extension-character-count": "^2.12.0", "@tiptap/extension-mention": "^2.11.9", "@tiptap/extension-placeholder": "^2.11.9", "@tiptap/react": "^2.11.9", "@tiptap/starter-kit": "^2.11.9", "@tiptap/suggestion": "^2.11.9", "@upstash/ratelimit": "^2.0.5", "@vercel/kv": "^3.0.0", "@vercel/og": "^0.6.8", "ai": "^4.3.13", "better-auth": "^1.2.7", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "cuid": "^3.0.0", "culori": "^4.0.1", "date-fns": "^4.1.0", "dotenv": "^16.5.0", "drizzle-orm": "^0.42.0", "embla-carousel-react": "^8.6.0", "idb-keyval": "^6.2.2", "input-otp": "^1.4.2", "isbot": "^5.1.26", "lexical": "^0.31.0", "lucide-react": "^0.488.0", "motion": "^12.7.3", "next": "15.4.1", "next-themes": "^0.4.6", "nuqs": "^2.4.3", "openai": "^4.96.2", "posthog-js": "^1.236.1", "react": "^19.0.0", "react-day-picker": "^9.6.7", "react-dom": "^19.0.0", "react-dropzone": "^14.3.8", "react-hook-form": "^7.55.0", "react-resizable-panels": "^2.1.7", "recharts": "^2.15.2", "screenfull": "^6.0.2", "server-only": "^0.0.1", "shadcn": "^2.5.0", "sonner": "^2.0.3", "swr": "^2.3.3", "tailwind-merge": "^3.2.0", "tippy.js": "^6.3.7", "use-scramble": "^2.2.15", "vaul": "^1.1.2", "zod": "^3.24.2", "zustand": "^5.0.3"}, "devDependencies": {"@eslint/eslintrc": "^3", "@ngard/tiny-isequal": "^1.1.0", "@svgr/webpack": "^8.1.0", "@tailwindcss/postcss": "^4", "@types/culori": "^2.1.1", "@types/next": "^9.0.0", "@types/node": "^20", "@types/pg": "^8.11.13", "@types/react": "^19", "@types/react-dom": "^19", "drizzle-kit": "^0.31.0", "eslint": "^9", "eslint-config-next": "15.3.0", "husky": "^9.1.7", "prettier": "^3.5.3", "prettier-plugin-tailwindcss": "^0.6.11", "raw-loader": "^4.0.2", "tailwindcss": "^4", "tsx": "^4.19.3", "tw-animate-css": "^1.2.5", "typescript": "^5"}}